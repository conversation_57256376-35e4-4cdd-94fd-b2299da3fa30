const axios = require("axios");
const { v4: uuidv4 } = require('uuid');
const WorkflowModel = require("../../model/WorkflowModel/WorkflowModel");
const VapiService = require("../../services/VapiService");

const VAPI_BASE_URL = "https://api.vapi.ai";
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY;

// List all workflows
const listWorkflows = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = "" } = req.query;
    const user_id = req.user.id;

    let vapiWorkflows = [];

    // Try to get workflows from Vapi API
    try {
      const response = await axios.get(`${VAPI_BASE_URL}/workflow`, {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        },
        params: {
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit)
        }
      });
      vapiWorkflows = Array.isArray(response.data) ? response.data : [];
    } catch (vapiError) {
      console.warn("Vapi API error, falling back to local workflows:", vapiError.response?.data || vapiError.message);
      // Continue with empty Vapi workflows array
    }

    // Get local workflow records
    const localWorkflows = await WorkflowModel.getWorkflowsByUserId(user_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      search
    });

    // If we have Vapi workflows, merge them with local data
    let mergedWorkflows = [];
    if (vapiWorkflows.length > 0) {
      mergedWorkflows = vapiWorkflows.map(vapiWorkflow => {
        const localWorkflow = localWorkflows.workflows.find(
          local => local.workflow_id === vapiWorkflow.id
        );
        return {
          ...vapiWorkflow,
          localData: localWorkflow || null
        };
      });
    } else {
      // If no Vapi workflows, return local workflows with mock structure
      mergedWorkflows = localWorkflows.workflows.map(localWorkflow => ({
        id: localWorkflow.workflow_id,
        name: localWorkflow.name,
        description: localWorkflow.description,
        nodes: [],
        edges: [],
        createdAt: localWorkflow.created_at,
        updatedAt: localWorkflow.updated_at,
        localData: localWorkflow
      }));
    }

    res.status(200).json({
      success: true,
      message: "Workflows retrieved successfully",
      data: {
        workflows: mergedWorkflows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(Math.max(vapiWorkflows.length, localWorkflows.workflows.length) / parseInt(limit)),
          totalItems: Math.max(vapiWorkflows.length, localWorkflows.workflows.length),
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error("Error listing workflows:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflows",
      error: error.message
    });
  }
};

// Get workflow by ID
const getWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    // Get local workflow data first
    const localWorkflow = await WorkflowModel.getWorkflowByWorkflowId(id);

    if (!localWorkflow) {
      return res.status(404).json({
        success: false,
        message: "Workflow not found"
      });
    }

    let vapiWorkflow = null;

    // Try to get workflow from Vapi API
    try {
      const response = await axios.get(`${VAPI_BASE_URL}/workflow/${id}`, {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      });
      vapiWorkflow = response.data;
    } catch (vapiError) {
      console.warn("Failed to fetch from VAPI API, using local data:", vapiError.response?.data || vapiError.message);

      // If VAPI fails, construct workflow from local data
      vapiWorkflow = {
        id: localWorkflow.workflow_id,
        orgId: localWorkflow.org_id,
        name: localWorkflow.name,
        description: localWorkflow.description,
        nodes: typeof localWorkflow.nodes === 'string' ? JSON.parse(localWorkflow.nodes) : localWorkflow.nodes,
        edges: typeof localWorkflow.edges === 'string' ? JSON.parse(localWorkflow.edges) : localWorkflow.edges,
        model: typeof localWorkflow.model === 'string' ? JSON.parse(localWorkflow.model) : localWorkflow.model,
        transcriber: typeof localWorkflow.transcriber === 'string' ? JSON.parse(localWorkflow.transcriber) : localWorkflow.transcriber,
        voice: typeof localWorkflow.voice === 'string' ? JSON.parse(localWorkflow.voice) : localWorkflow.voice,
        globalPrompt: localWorkflow.global_prompt,
        backgroundSound: localWorkflow.background_sound,
        credentials: typeof localWorkflow.credentials === 'string' ? JSON.parse(localWorkflow.credentials) : localWorkflow.credentials,
        credentialIds: typeof localWorkflow.credential_ids === 'string' ? JSON.parse(localWorkflow.credential_ids) : localWorkflow.credential_ids,
        createdAt: localWorkflow.created_at,
        updatedAt: localWorkflow.updated_at
      };
    }

    res.status(200).json({
      success: true,
      message: "Workflow retrieved successfully",
      data: {
        ...vapiWorkflow,
        localData: localWorkflow
      }
    });
  } catch (error) {
    console.error("Error getting workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflow",
      error: error.message
    });
  }
};

// Create workflow
const createWorkflow = async (req, res) => {
  try {
    // Handle different token structures
    let user_id;
    if (req.user && req.user.id) {
      user_id = req.user.id;
    } else if (req.user && req.user.user && req.user.user.id) {
      user_id = req.user.user.id;
    } else {
      console.error("Authentication failed - user not found in token");
      return res.status(401).json({
        success: false,
        message: "Authentication failed - user not found in token"
      });
    }
    const workflowData = req.body;

    // Extract description for local storage, remove from Vapi data
    const { description, ...vapiWorkflowData } = workflowData;

    let vapiWorkflow = null;

    // Try to create workflow in Vapi API
    try {
      if (VAPI_SECRET_KEY) {
        const response = await axios.post(`${VAPI_BASE_URL}/workflow`, vapiWorkflowData, {
          headers: {
            Authorization: `Bearer ${VAPI_SECRET_KEY}`,
            "Content-Type": "application/json"
          }
        });
        vapiWorkflow = response.data;
      } else {
        console.warn("VAPI_SECRET_KEY not configured, creating local workflow only");
        throw new Error("VAPI not configured");
      }
    } catch (vapiError) {
      console.warn("Failed to create workflow in VAPI, creating local workflow:", vapiError.response?.data || vapiError.message);

      // Create a local workflow with generated ID
      vapiWorkflow = {
        id: uuidv4(),
        orgId: "local",
        name: workflowData.name,
        nodes: workflowData.nodes || [],
        edges: workflowData.edges || [],
        model: workflowData.model || null,
        transcriber: workflowData.transcriber || null,
        voice: workflowData.voice || null,
        globalPrompt: workflowData.globalPrompt || "",
        backgroundSound: workflowData.backgroundSound || "off",
        credentials: workflowData.credentials || null,
        credentialIds: workflowData.credentialIds || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    }

    // Store workflow reference in local database
    const localWorkflowData = {
      user_id,
      workflow_id: vapiWorkflow.id,
      vapi_workflow_id: vapiWorkflow.id,
      org_id: vapiWorkflow.orgId || "synced_from_vapi",
      name: vapiWorkflow.name,
      description: description || "",
      nodes: vapiWorkflow.nodes || [],
      edges: vapiWorkflow.edges || [],
      model: vapiWorkflow.model || null,
      transcriber: vapiWorkflow.transcriber || null,
      voice: vapiWorkflow.voice || null,
      global_prompt: vapiWorkflow.globalPrompt || "",
      background_sound: vapiWorkflow.backgroundSound || "off",
      credentials: vapiWorkflow.credentials || null,
      credential_ids: vapiWorkflow.credentialIds || null,
      variables: {},
      triggers: [],
      status: "active",
      version: "1.0.0",
      tags: [],
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString()
      },
      execution_count: 0
    };

    const localWorkflowId = await WorkflowModel.createWorkflow(localWorkflowData);

    res.status(201).json({
      success: true,
      message: "Workflow created successfully",
      data: {
        ...vapiWorkflow,
        localData: { id: localWorkflowId, ...localWorkflowData }
      }
    });
  } catch (error) {
    console.error("Error creating workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to create workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Update workflow
const updateWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;
    const workflowData = req.body;

    // Update workflow in Vapi API
    const response = await axios.patch(`${VAPI_BASE_URL}/workflow/${id}`, workflowData, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    const vapiWorkflow = response.data;

    // Update local workflow data
    const localWorkflowData = {
      name: vapiWorkflow.name,
      description: workflowData.description || "",
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString()
      }
    };

    await WorkflowModel.updateWorkflowByWorkflowId(id, localWorkflowData);

    res.status(200).json({
      success: true,
      message: "Workflow updated successfully",
      data: vapiWorkflow
    });
  } catch (error) {
    console.error("Error updating workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to update workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Delete workflow
const deleteWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    // Delete workflow from Vapi API
    await axios.delete(`${VAPI_BASE_URL}/workflow/${id}`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    // Delete local workflow data
    await WorkflowModel.deleteWorkflowByWorkflowId(id);

    res.status(200).json({
      success: true,
      message: "Workflow deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to delete workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Test workflow (create call)
const testWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const { phoneNumber, metadata = {} } = req.body;

    // Create call with workflow - using proper Vapi format
    const callData = {
      workflowId: id,
      phoneNumberId: process.env.VAPI_PHONE_NUMBER_ID, // Use configured phone number ID
      customer: {
        number: phoneNumber || "+15551234567" // Customer number to call
      },
      metadata: {
        ...metadata,
        testCall: true,
        timestamp: new Date().toISOString()
      }
    };

    const call = await VapiService.createCall(callData);

    res.status(200).json({
      success: true,
      message: "Workflow test call initiated successfully",
      data: call
    });
  } catch (error) {
    console.error("Error testing workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to test workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Get workflow templates
const getWorkflowTemplates = async (req, res) => {
  try {
    const templates = [
      {
        id: "basic-greeting",
        name: "Basic Greeting Workflow",
        description: "Simple greeting workflow with information collection",
        category: "Basic",
        nodes: [
          {
            type: "conversation",
            name: "greeting",
            isStart: true,
            prompt: "Greet the user and ask how you can help them today."
          }
        ],
        edges: []
      },
      {
        id: "appointment-scheduling",
        name: "Appointment Scheduling",
        description: "Complete appointment booking workflow",
        category: "Business",
        nodes: [
          {
            type: "conversation",
            name: "greeting",
            isStart: true,
            prompt: "Greet the user and ask about scheduling an appointment."
          }
        ],
        edges: []
      },
      {
        id: "customer-support",
        name: "Customer Support",
        description: "Customer support workflow with escalation",
        category: "Support",
        nodes: [
          {
            type: "conversation",
            name: "support-greeting",
            isStart: true,
            prompt: "Greet the customer and ask about their issue."
          }
        ],
        edges: []
      }
    ];

    res.status(200).json({
      success: true,
      message: "Workflow templates retrieved successfully",
      data: templates
    });
  } catch (error) {
    console.error("Error getting workflow templates:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflow templates",
      error: error.message
    });
  }
};

// VAPI-specific endpoints
const createVapiWorkflow = async (req, res) => {
  try {
    const user_id = req.user.id;
    const workflowData = req.body;

    // Create workflow in VAPI
    const vapiWorkflow = await VapiService.createWorkflow(workflowData);

    // Store workflow reference in local database
    const localWorkflowData = {
      user_id,
      workflow_id: vapiWorkflow.id,
      vapi_workflow_id: vapiWorkflow.id,
      org_id: vapiWorkflow.orgId || "vapi_created",
      name: vapiWorkflow.name,
      description: workflowData.description || "",
      nodes: vapiWorkflow.nodes || [],
      edges: vapiWorkflow.edges || [],
      model: vapiWorkflow.model || null,
      transcriber: vapiWorkflow.transcriber || null,
      voice: vapiWorkflow.voice || null,
      global_prompt: vapiWorkflow.globalPrompt || "",
      background_sound: vapiWorkflow.backgroundSound || "off",
      credentials: vapiWorkflow.credentials || null,
      credential_ids: vapiWorkflow.credentialIds || null,
      variables: {},
      triggers: [],
      status: "active",
      version: "1.0.0",
      tags: [],
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString(),
        createdVia: "vapi_api"
      }
    };

    await WorkflowModel.createWorkflow(localWorkflowData);

    res.status(201).json({
      success: true,
      message: "VAPI workflow created successfully",
      data: vapiWorkflow
    });
  } catch (error) {
    console.error("Error creating VAPI workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create VAPI workflow",
      error: error.message
    });
  }
};

const getVapiWorkflow = async (req, res) => {
  try {
    const { id } = req.params;

    // Get workflow from VAPI
    const vapiWorkflow = await VapiService.getWorkflow(id);

    // Get local workflow data
    const localWorkflow = await WorkflowModel.getWorkflowByWorkflowId(id);

    res.status(200).json({
      success: true,
      message: "VAPI workflow retrieved successfully",
      data: {
        ...vapiWorkflow,
        localData: localWorkflow
      }
    });
  } catch (error) {
    console.error("Error fetching VAPI workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch VAPI workflow",
      error: error.message
    });
  }
};

const updateVapiWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const workflowData = req.body;

    // Update workflow in VAPI
    const vapiWorkflow = await VapiService.updateWorkflow(id, workflowData);

    // Update local workflow data
    const localWorkflowData = {
      name: vapiWorkflow.name,
      description: workflowData.description || "",
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString()
      }
    };

    await WorkflowModel.updateWorkflowByWorkflowId(id, localWorkflowData);

    res.status(200).json({
      success: true,
      message: "VAPI workflow updated successfully",
      data: vapiWorkflow
    });
  } catch (error) {
    console.error("Error updating VAPI workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update VAPI workflow",
      error: error.message
    });
  }
};

const deleteVapiWorkflow = async (req, res) => {
  try {
    const { id } = req.params;

    // Delete workflow from VAPI
    await VapiService.deleteWorkflow(id);

    // Delete local workflow data
    await WorkflowModel.deleteWorkflowByWorkflowId(id);

    res.status(200).json({
      success: true,
      message: "VAPI workflow deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting VAPI workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete VAPI workflow",
      error: error.message
    });
  }
};

const createVapiCall = async (req, res) => {
  try {
    const callData = req.body;

    // Create call using VAPI service
    const call = await VapiService.createCall(callData);

    res.status(200).json({
      success: true,
      message: "VAPI call created successfully",
      data: call
    });
  } catch (error) {
    console.error("Error creating VAPI call:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create VAPI call",
      error: error.message
    });
  }
};

const validateVapiWorkflow = async (req, res) => {
  try {
    const workflowData = req.body;

    // Validate workflow using VAPI service
    const validation = VapiService.validateWorkflow(workflowData);

    res.status(200).json({
      success: true,
      message: "Workflow validation completed",
      data: validation
    });
  } catch (error) {
    console.error("Error validating VAPI workflow:", error);
    res.status(500).json({
      success: false,
      message: "Failed to validate workflow",
      error: error.message
    });
  }
};

module.exports = {
  listWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  testWorkflow,
  getWorkflowTemplates,
  // VAPI-specific endpoints
  createVapiWorkflow,
  getVapiWorkflow,
  updateVapiWorkflow,
  deleteVapiWorkflow,
  createVapiCall,
  validateVapiWorkflow
};
