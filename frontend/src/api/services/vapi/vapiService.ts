import axiosInstance from "../../axios/axiosInstance";
import { toast } from "sonner";
import { Workflow, WorkflowNode, WorkflowEdge } from "@/types/api";

// VAPI API Base Configuration
const VAPI_BASE_URL = process.env.REACT_APP_VAPI_BASE_URL || "https://api.vapi.ai";

// VAPI Workflow API Service
export class VapiWorkflowService {
  // Create workflow in VAPI
  static async createWorkflow(workflowData: Partial<Workflow>): Promise<Workflow> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi", workflowData);
      return response.data.data;
    } catch (error: any) {
      console.error("Error creating VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to create workflow";
      toast.error(message);
      throw error;
    }
  }

  // Get workflow from VAPI
  static async getWorkflow(workflowId: string): Promise<Workflow> {
    try {
      const response = await axiosInstance.get(`/api/workflows/vapi/${workflowId}`);
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to fetch workflow";
      toast.error(message);
      throw error;
    }
  }

  // Update workflow in VAPI
  static async updateWorkflow(workflowId: string, workflowData: Partial<Workflow>): Promise<Workflow> {
    try {
      const response = await axiosInstance.patch(`/api/workflows/vapi/${workflowId}`, workflowData);
      return response.data.data;
    } catch (error: any) {
      console.error("Error updating VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to update workflow";
      toast.error(message);
      throw error;
    }
  }

  // Delete workflow from VAPI
  static async deleteWorkflow(workflowId: string): Promise<void> {
    try {
      await axiosInstance.delete(`/api/workflows/vapi/${workflowId}`);
      toast.success("Workflow deleted successfully");
    } catch (error: any) {
      console.error("Error deleting VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to delete workflow";
      toast.error(message);
      throw error;
    }
  }

  // List all workflows from VAPI
  static async listWorkflows(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{ workflows: Workflow[]; total: number; page: number; limit: number }> {
    try {
      const response = await axiosInstance.get("/api/workflows/vapi", { params });
      return response.data.data;
    } catch (error: any) {
      console.error("Error listing VAPI workflows:", error);
      const message = error.response?.data?.message || "Failed to list workflows";
      toast.error(message);
      throw error;
    }
  }

  // Create a call with workflow
  static async createCall(callData: {
    workflowId: string;
    phoneNumberId?: string;
    customer: {
      number: string;
      name?: string;
    };
    metadata?: any;
  }): Promise<any> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi/calls", callData);
      toast.success("Call initiated successfully");
      return response.data.data;
    } catch (error: any) {
      console.error("Error creating VAPI call:", error);
      const message = error.response?.data?.message || "Failed to create call";
      toast.error(message);
      throw error;
    }
  }

  // Get workflow templates
  static async getTemplates(): Promise<any[]> {
    try {
      const response = await axiosInstance.get("/api/workflows/vapi/templates");
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching VAPI templates:", error);
      const message = error.response?.data?.message || "Failed to fetch templates";
      toast.error(message);
      throw error;
    }
  }

  // Validate workflow structure
  static async validateWorkflow(workflowData: Workflow): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi/validate", workflowData);
      return response.data.data;
    } catch (error: any) {
      console.error("Error validating VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to validate workflow";
      toast.error(message);
      throw error;
    }
  }

  // Auto-save workflow (silent operation)
  static async autoSaveWorkflow(workflowId: string, workflowData: Partial<Workflow>): Promise<void> {
    try {
      await axiosInstance.patch(`/api/workflows/vapi/${workflowId}/autosave`, workflowData);
    } catch (error: any) {
      console.warn("Auto-save failed:", error);
      // Silent failure for auto-save
    }
  }

  // Duplicate workflow
  static async duplicateWorkflow(workflowId: string, newName: string): Promise<Workflow> {
    try {
      const response = await axiosInstance.post(`/api/workflows/vapi/${workflowId}/duplicate`, {
        name: newName
      });
      toast.success("Workflow duplicated successfully");
      return response.data.data;
    } catch (error: any) {
      console.error("Error duplicating VAPI workflow:", error);
      const message = error.response?.data?.message || "Failed to duplicate workflow";
      toast.error(message);
      throw error;
    }
  }
}

// VAPI Node Configuration Service
export class VapiNodeService {
  // Get available node types and their configurations
  static async getNodeTypes(): Promise<any[]> {
    try {
      const response = await axiosInstance.get("/api/workflows/vapi/node-types");
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching VAPI node types:", error);
      throw error;
    }
  }

  // Get node configuration schema
  static async getNodeSchema(nodeType: string): Promise<any> {
    try {
      const response = await axiosInstance.get(`/api/workflows/vapi/node-types/${nodeType}/schema`);
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching VAPI node schema:", error);
      throw error;
    }
  }

  // Validate node configuration
  static async validateNode(nodeData: WorkflowNode): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi/nodes/validate", nodeData);
      return response.data.data;
    } catch (error: any) {
      console.error("Error validating VAPI node:", error);
      throw error;
    }
  }
}

// VAPI Variable Service
export class VapiVariableService {
  // Extract variables from conversation
  static async extractVariables(conversationText: string, schema: any): Promise<Record<string, any>> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi/variables/extract", {
        text: conversationText,
        schema
      });
      return response.data.data;
    } catch (error: any) {
      console.error("Error extracting variables:", error);
      throw error;
    }
  }

  // Validate variable schema
  static async validateSchema(schema: any): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    try {
      const response = await axiosInstance.post("/api/workflows/vapi/variables/validate-schema", {
        schema
      });
      return response.data.data;
    } catch (error: any) {
      console.error("Error validating variable schema:", error);
      throw error;
    }
  }
}

export default VapiWorkflowService;
