import { apiClient } from '@/api/client';

export interface VapiNodeType {
  type: string;
  label: string;
  description: string;
  icon: string;
  color: string;
  category: string;
  configuration: {
    fields: Array<{
      name: string;
      type: 'text' | 'textarea' | 'select' | 'number' | 'boolean';
      label: string;
      description?: string;
      required?: boolean;
      options?: Array<{ label: string; value: string }>;
      defaultValue?: any;
    }>;
  };
}

// Mock VAPI node types (in real implementation, this would come from VAPI API)
const VAPI_NODE_TYPES: VapiNodeType[] = [
  {
    type: 'conversation',
    label: 'Conversation',
    description: 'AI conversation with users',
    icon: 'MessageCircle',
    color: 'bg-blue-100 text-blue-600',
    category: 'core',
    configuration: {
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Node Name',
          description: 'Name for this conversation node',
          required: true,
          defaultValue: 'Conversation'
        },
        {
          name: 'firstMessage',
          type: 'text',
          label: 'First Message',
          description: 'Initial message to send to the user',
          required: false,
          defaultValue: 'Hello! How can I help you today?'
        },
        {
          name: 'prompt',
          type: 'textarea',
          label: 'System Prompt',
          description: 'Instructions for the AI assistant',
          required: true,
          defaultValue: 'You are a helpful AI assistant. Respond naturally and helpfully to user queries.'
        },
        {
          name: 'model',
          type: 'select',
          label: 'AI Model',
          description: 'Choose the AI model to use',
          required: true,
          options: [
            { label: 'GPT-4', value: 'gpt-4' },
            { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
            { label: 'Claude 3', value: 'claude-3' }
          ],
          defaultValue: 'gpt-4'
        },
        {
          name: 'temperature',
          type: 'number',
          label: 'Temperature',
          description: 'Controls randomness (0.0 to 1.0)',
          required: false,
          defaultValue: 0.7
        }
      ]
    }
  },
  {
    type: 'apiRequest',
    label: 'API Request',
    description: 'Make HTTP requests',
    icon: 'Zap',
    color: 'bg-purple-100 text-purple-600',
    category: 'integration',
    configuration: {
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Node Name',
          required: true,
          defaultValue: 'API Request'
        },
        {
          name: 'method',
          type: 'select',
          label: 'HTTP Method',
          required: true,
          options: [
            { label: 'GET', value: 'GET' },
            { label: 'POST', value: 'POST' },
            { label: 'PUT', value: 'PUT' },
            { label: 'DELETE', value: 'DELETE' }
          ],
          defaultValue: 'GET'
        },
        {
          name: 'url',
          type: 'text',
          label: 'URL',
          description: 'API endpoint URL',
          required: true,
          defaultValue: ''
        },
        {
          name: 'headers',
          type: 'textarea',
          label: 'Headers (JSON)',
          description: 'HTTP headers as JSON object',
          required: false,
          defaultValue: '{}'
        },
        {
          name: 'body',
          type: 'textarea',
          label: 'Request Body (JSON)',
          description: 'Request body for POST/PUT requests',
          required: false,
          defaultValue: '{}'
        }
      ]
    }
  },
  {
    type: 'transferCall',
    label: 'Transfer Call',
    description: 'Transfer to another number',
    icon: 'PhoneCall',
    color: 'bg-green-100 text-green-600',
    category: 'telephony',
    configuration: {
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Node Name',
          required: true,
          defaultValue: 'Transfer Call'
        },
        {
          name: 'destination',
          type: 'text',
          label: 'Destination Number',
          description: 'Phone number to transfer to',
          required: true,
          defaultValue: ''
        },
        {
          name: 'transferMessage',
          type: 'text',
          label: 'Transfer Message',
          description: 'Message to play before transfer',
          required: false,
          defaultValue: 'Please hold while I transfer you.'
        },
        {
          name: 'summary',
          type: 'textarea',
          label: 'Call Summary',
          description: 'Summary to provide to the receiving party',
          required: false,
          defaultValue: ''
        }
      ]
    }
  },
  {
    type: 'endCall',
    label: 'End Call',
    description: 'Terminate the call',
    icon: 'StopCircle',
    color: 'bg-red-100 text-red-600',
    category: 'telephony',
    configuration: {
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Node Name',
          required: true,
          defaultValue: 'End Call'
        },
        {
          name: 'endMessage',
          type: 'text',
          label: 'End Message',
          description: 'Final message before ending call',
          required: false,
          defaultValue: 'Thank you for calling. Have a great day!'
        }
      ]
    }
  },
  {
    type: 'tool',
    label: 'Tool',
    description: 'Use external tools',
    icon: 'Wrench',
    color: 'bg-orange-100 text-orange-600',
    category: 'integration',
    configuration: {
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Node Name',
          required: true,
          defaultValue: 'Tool'
        },
        {
          name: 'toolType',
          type: 'select',
          label: 'Tool Type',
          required: true,
          options: [
            { label: 'Web Search', value: 'web_search' },
            { label: 'Calculator', value: 'calculator' },
            { label: 'Weather', value: 'weather' },
            { label: 'Custom Function', value: 'custom' }
          ],
          defaultValue: 'web_search'
        },
        {
          name: 'configuration',
          type: 'textarea',
          label: 'Tool Configuration (JSON)',
          description: 'Tool-specific configuration',
          required: false,
          defaultValue: '{}'
        }
      ]
    }
  }
];

export const getVapiNodeTypes = async (): Promise<VapiNodeType[]> => {
  try {
    // In a real implementation, this would be:
    // const response = await apiClient.get('/vapi/node-types');
    // return response.data;
    
    // For now, return mock data
    return new Promise((resolve) => {
      setTimeout(() => resolve(VAPI_NODE_TYPES), 500); // Simulate API delay
    });
  } catch (error) {
    console.error('Error fetching VAPI node types:', error);
    throw error;
  }
};

export const getNodeTypeConfiguration = async (nodeType: string): Promise<VapiNodeType | null> => {
  try {
    const nodeTypes = await getVapiNodeTypes();
    return nodeTypes.find(type => type.type === nodeType) || null;
  } catch (error) {
    console.error('Error fetching node type configuration:', error);
    throw error;
  }
};
