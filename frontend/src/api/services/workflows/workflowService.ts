import axiosInstance from "../../axios/axiosInstance";
import { toast } from "sonner";

// Import types from centralized location
import { Workflow, WorkflowNode, WorkflowEdge } from "@/types/api";

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  preview?: string;
  tags?: string[];
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  callId?: string;
  executionType: 'test' | 'production';
  status: 'queued' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  phoneNumber?: string;
  duration: number;
  cost: number;
  metadata?: any;
  startedAt?: string;
  endedAt?: string;
  createdAt: string;
}

export interface VapiCallRequest {
  workflowId: string;
  phoneNumberId?: string;
  customer?: {
    number: string;
    name?: string;
  };
  metadata?: any;
}

// Get all workflows
export async function getAllWorkflows(page?: number, search?: string, status?: string) {
  try {
    const res = await axiosInstance.get("/api/workflows", {
      params: { page, search, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching workflows:", error);
    toast.error("Failed to fetch workflows");
    throw error;
  }
}

// Get workflow by ID
export async function getWorkflowById(id: string) {
  try {
    console.log("Fetching workflow from API:", id);
    const res = await axiosInstance.get(`/api/workflows/${id}`);
    console.log("Workflow API response:", res.data);
    return res.data;
  } catch (error: any) {
    console.error("Error fetching workflow:", error);
    console.error("Error response:", error.response?.data);
    toast.error("Failed to fetch workflow");
    throw error;
  }
}

// Create workflow
export async function createWorkflow(data: Partial<Workflow>) {
  try {
    console.log("Creating workflow with data:", data);
    const res = await axiosInstance.post("/api/workflows", data);
    console.log("Create workflow response:", res.data);
    toast.success("Workflow created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating workflow:", error);
    console.error("Error response:", error.response?.data);
    const message = error.response?.data?.message || "Failed to create workflow";
    toast.error(message);
    throw error;
  }
}

// Update workflow
export async function updateWorkflow(id: string, data: Partial<Workflow>) {
  try {
    const res = await axiosInstance.patch(`/api/workflows/${id}`, data);
    toast.success("Workflow updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating workflow:", error);
    const message = error.response?.data?.message || "Failed to update workflow";
    toast.error(message);
    throw error;
  }
}

// Delete workflow
export async function deleteWorkflow(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/workflows/${id}`);
    toast.success("Workflow deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting workflow:", error);
    const message = error.response?.data?.message || "Failed to delete workflow";
    toast.error(message);
    throw error;
  }
}

// Test workflow (create call)
export async function testWorkflow(id: string, phoneNumber?: string, metadata?: any) {
  try {
    const res = await axiosInstance.post(`/api/workflows/${id}/test`, {
      phoneNumber,
      metadata,
    });
    toast.success("Workflow test call initiated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing workflow:", error);
    const message = error.response?.data?.message || "Failed to test workflow";
    toast.error(message);
    throw error;
  }
}

// Get workflow templates
export async function getWorkflowTemplates() {
  try {
    const res = await axiosInstance.get("/api/workflows/templates/all");
    return res.data;
  } catch (error) {
    console.error("Error fetching workflow templates:", error);
    toast.error("Failed to fetch workflow templates");
    throw error;
  }
}

// Search workflows
export async function searchWorkflows(searchTerm: string, page?: number, limit?: number) {
  try {
    const res = await axiosInstance.get("/api/workflows", {
      params: { search: searchTerm, page, limit },
    });
    return res.data;
  } catch (error) {
    console.error("Error searching workflows:", error);
    toast.error("Failed to search workflows");
    throw error;
  }
}

// Get workflow statistics
export async function getWorkflowStats() {
  try {
    const res = await axiosInstance.get("/api/workflows/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching workflow stats:", error);
    toast.error("Failed to fetch workflow statistics");
    throw error;
  }
}

// Auto-save workflow (silent update without toast)
export async function autoSaveWorkflow(id: string, data: Partial<Workflow>) {
  try {
    const res = await axiosInstance.patch(`/api/workflows/${id}`, data);
    return res.data;
  } catch (error: any) {
    console.error("Error auto-saving workflow:", error);
    // Silent failure for auto-save
    throw error;
  }
}

// Export workflow
export async function exportWorkflow(id: string) {
  try {
    const workflow = await getWorkflowById(id);
    const exportData = {
      ...workflow.data,
      exportedAt: new Date().toISOString(),
      version: "1.0"
    };
    
    // Create and download JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `workflow-${workflow.data.name}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast.success("Workflow exported successfully");
    return exportData;
  } catch (error: any) {
    console.error("Error exporting workflow:", error);
    const message = error.response?.data?.message || "Failed to export workflow";
    toast.error(message);
    throw error;
  }
}

// Import workflow
export async function importWorkflow(file: File) {
  try {
    const text = await file.text();
    const workflowData = JSON.parse(text);
    
    // Remove ID and timestamps for import
    const { id, createdAt, updatedAt, exportedAt, version, localData, ...importData } = workflowData;
    
    // Add import suffix to name to avoid conflicts
    importData.name = `${importData.name} (Imported)`;
    
    const res = await createWorkflow(importData);
    toast.success("Workflow imported successfully");
    return res;
  } catch (error: any) {
    console.error("Error importing workflow:", error);
    const message = error.response?.data?.message || "Failed to import workflow";
    toast.error(message);
    throw error;
  }
}

// VAPI-specific API functions
export async function createVapiCall(callRequest: VapiCallRequest) {
  try {
    const res = await axiosInstance.post("/api/workflows/vapi/calls", callRequest);
    toast.success("Call initiated successfully");
    return res.data;
  } catch (error) {
    console.error("Error creating VAPI call:", error);
    toast.error("Failed to create call");
    throw error;
  }
}

export async function getVapiWorkflowTemplates() {
  try {
    const res = await axiosInstance.get("/api/workflows/vapi/templates");
    return res.data;
  } catch (error) {
    console.error("Error fetching VAPI templates:", error);
    toast.error("Failed to fetch workflow templates");
    throw error;
  }
}

export async function validateWorkflow(workflowId: string) {
  try {
    const res = await axiosInstance.post(`/api/workflows/${workflowId}/validate`);
    return res.data;
  } catch (error) {
    console.error("Error validating workflow:", error);
    toast.error("Failed to validate workflow");
    throw error;
  }
}

export async function duplicateWorkflow(workflowId: string, newName: string) {
  try {
    const res = await axiosInstance.post(`/api/workflows/${workflowId}/duplicate`, {
      name: newName
    });
    toast.success("Workflow duplicated successfully");
    return res.data;
  } catch (error) {
    console.error("Error duplicating workflow:", error);
    toast.error("Failed to duplicate workflow");
    throw error;
  }
}
