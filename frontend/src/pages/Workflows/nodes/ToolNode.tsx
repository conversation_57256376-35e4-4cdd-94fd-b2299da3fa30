import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Wrench,
  Settings,
  Trash2,
} from "lucide-react";

interface ToolNodeData {
  name: string;
  toolId?: string;
  toolName?: string;
  toolType?: string;
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ToolNode = ({ data, selected }: NodeProps<ToolNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  // Mock tools data - in real implementation, this would come from an API
  const availableTools = [
    { id: "tool-1", name: "Calendar Booking", type: "API Request" },
    { id: "tool-2", name: "CRM Lookup", type: "Database" },
    { id: "tool-3", name: "Email Sender", type: "Communication" },
    { id: "tool-4", name: "Weather API", type: "External API" },
    { id: "tool-5", name: "Calculator", type: "Utility" },
  ];

  const handleUpdate = useCallback((updates: Partial<ToolNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleToolSelect = (toolId: string) => {
    const selectedTool = availableTools.find(tool => tool.id === toolId);
    if (selectedTool) {
      handleUpdate({
        toolId,
        toolName: selectedTool.name,
        toolType: selectedTool.type,
      });
    }
  };

  const getToolTypeColor = (type: string) => {
    switch (type) {
      case "API Request":
        return "bg-blue-100 text-blue-800";
      case "Database":
        return "bg-green-100 text-green-800";
      case "Communication":
        return "bg-purple-100 text-purple-800";
      case "External API":
        return "bg-orange-100 text-orange-800";
      case "Utility":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-purple-500"
      />
      
      <Card
        className={`min-w-[280px] max-w-[320px] transition-all duration-200 ${
          selected
            ? "ring-2 ring-purple-500 shadow-lg"
            : "shadow-md hover:shadow-lg"
        }`}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wrench className="h-4 w-4 text-purple-600" />
              <CardTitle className="text-sm font-medium">
                {localData.name || "Tool"}
              </CardTitle>
            </div>
            <div className="flex items-center gap-1">
              {localData.toolType && (
                <Badge className={getToolTypeColor(localData.toolType)}>
                  {localData.toolType}
                </Badge>
              )}
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure Tool Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="tool-select">Select Tool</Label>
                      <Select
                        value={localData.toolId || ""}
                        onValueChange={handleToolSelect}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableTools.map((tool) => (
                            <SelectItem key={tool.id} value={tool.id}>
                              <div className="flex items-center gap-2">
                                <Wrench className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{tool.name}</div>
                                  <div className="text-xs text-gray-500">{tool.type}</div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {localData.toolId && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Selected Tool</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <strong>Name:</strong> {localData.toolName}
                          </div>
                          <div className="flex items-center gap-2">
                            <strong>Type:</strong> 
                            <Badge className={getToolTypeColor(localData.toolType || "")}>
                              {localData.toolType}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <strong>ID:</strong> 
                            <code className="text-xs bg-gray-200 px-2 py-1 rounded">
                              {localData.toolId}
                            </code>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            {localData.toolName ? (
              <div className="flex items-center gap-2 text-xs">
                <Wrench className="h-3 w-3 text-gray-500" />
                <span className="text-gray-700">{localData.toolName}</span>
              </div>
            ) : (
              <div className="text-xs text-gray-500 italic">
                No tool selected
              </div>
            )}
            
            {localData.toolId && (
              <div className="text-xs text-gray-600">
                <strong>Tool ID:</strong> {localData.toolId}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-purple-500"
      />
    </>
  );
};

export default ToolNode;
