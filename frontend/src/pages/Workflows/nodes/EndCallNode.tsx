import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  PhoneOff,
  Settings,
  Trash2,
} from "lucide-react";

interface EndCallNodeData {
  name: string;
  firstMessage?: string;
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const EndCallNode = ({ data, selected }: NodeProps<EndCallNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<EndCallNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-red-500"
      />
      
      <Card
        className={`min-w-[280px] max-w-[320px] transition-all duration-200 ${
          selected
            ? "ring-2 ring-red-500 shadow-lg"
            : "shadow-md hover:shadow-lg"
        }`}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <PhoneOff className="h-4 w-4 text-red-600" />
              <CardTitle className="text-sm font-medium">
                {localData.name || "End Call"}
              </CardTitle>
            </div>
            <div className="flex items-center gap-1">
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure End Call Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="first-message">Closing Message (Optional)</Label>
                      <Textarea
                        id="first-message"
                        value={localData.firstMessage || ""}
                        onChange={(e) => handleFirstMessageChange(e.target.value)}
                        placeholder="Final message to say before ending the call"
                        rows={3}
                      />
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="text-xs text-gray-700">
              <strong>Action:</strong> Terminates the call
            </div>
            
            {localData.firstMessage && (
              <div className="text-xs text-gray-600 bg-red-50 p-2 rounded">
                <strong>Closing:</strong> {localData.firstMessage.substring(0, 60)}
                {localData.firstMessage.length > 60 && "..."}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* End call nodes don't have outgoing connections */}
    </>
  );
};

export default EndCallNode;
