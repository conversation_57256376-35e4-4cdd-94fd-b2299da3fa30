import { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  MessageCircle,
  Settings,
  Trash2,
  Star,
  Play,
  Plus,
  X,
  Globe,
  Zap,
  Variable,
} from "lucide-react";

interface VapiVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'integer';
  description: string;
  enum?: string[];
  required?: boolean;
}

interface ConversationNodeData {
  name: string;
  prompt: string;
  firstMessage?: string;
  isStart?: boolean;

  // Variable extraction
  variableExtractionPlan?: {
    schema: {
      type: 'object';
      properties: Record<string, {
        type: string;
        description: string;
        enum?: string[];
      }>;
      required?: string[];
    };
    aliases?: Array<{ key: string; value: string }>;
  };
  variables?: VapiVariable[];

  // Global node functionality
  globalNodePlan?: {
    enabled: boolean;
    enterCondition: string;
  };

  // Model configuration
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };

  // Voice configuration
  voice?: {
    provider: string;
    voiceId: string;
  };

  // Transcriber configuration
  transcriber?: {
    provider: string;
    model?: string;
    language?: string;
  };

  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ConversationNode = ({ data, selected }: NodeProps<ConversationNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);
  const [activeTab, setActiveTab] = useState("basic");

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  const handleUpdate = useCallback((updates: Partial<ConversationNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handlePromptChange = (value: string) => {
    handleUpdate({ prompt: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleStartToggle = (checked: boolean) => {
    handleUpdate({ isStart: checked });
  };

  const handleGlobalToggle = (checked: boolean) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enabled: checked,
      },
    });
  };

  const handleGlobalConditionChange = (value: string) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enterCondition: value,
      },
    });
  };

  const addVariable = () => {
    const newVariable: VapiVariable = {
      name: `variable_${(localData.variables?.length || 0) + 1}`,
      type: 'string',
      description: '',
      required: false,
    };

    const updatedVariables = [...(localData.variables || []), newVariable];
    handleUpdate({ variables: updatedVariables });
  };

  const updateVariable = (index: number, updates: Partial<VapiVariable>) => {
    const updatedVariables = [...(localData.variables || [])];
    updatedVariables[index] = { ...updatedVariables[index], ...updates };
    handleUpdate({ variables: updatedVariables });
  };

  const removeVariable = (index: number) => {
    const updatedVariables = [...(localData.variables || [])];
    updatedVariables.splice(index, 1);
    handleUpdate({ variables: updatedVariables });
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />

      <Card
        className={`min-w-[280px] max-w-[320px] transition-all duration-200 bg-white border rounded-lg ${
          selected
            ? "border-blue-500 shadow-lg"
            : "border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md"
        } ${localData.isStart ? "border-green-500 bg-green-50" : ""}`}
        onMouseDown={(e) => {
          // Allow dragging only if not clicking on buttons
          const target = e.target as HTMLElement;
          if (target.closest('button') || target.closest('[role="button"]')) {
            e.stopPropagation();
          }
        }}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-gray-900">
                  {localData.name || "Conversation"}
                </CardTitle>
                <div className="flex items-center gap-1 mt-1">
                  {localData.isStart && (
                    <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                      <Star className="h-3 w-3 mr-1" />
                      Start
                    </Badge>
                  )}
                  {localData.globalNodePlan?.enabled && (
                    <Badge variant="outline" className="text-xs border-orange-200 text-orange-700">
                      <Globe className="h-3 w-3 mr-1" />
                      Global
                    </Badge>
                  )}
                  {localData.variables && localData.variables.length > 0 && (
                    <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                      <Variable className="h-3 w-3 mr-1" />
                      {localData.variables.length}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-gray-100 rounded-md"
                    onClick={(e) => {
                      console.log('Settings button clicked');
                      e.stopPropagation();
                      e.preventDefault();
                      setIsConfigOpen(true);
                    }}
                  >
                    <Settings className="h-4 w-4 text-gray-500" />
                  </Button>
                </DialogTrigger>

                <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
                  <DialogHeader>
                    <DialogTitle className="text-lg font-semibold">Configure Conversation Node</DialogTitle>
                  </DialogHeader>

                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="basic">Basic</TabsTrigger>
                      <TabsTrigger value="variables">Variables</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced</TabsTrigger>
                      <TabsTrigger value="global">Global</TabsTrigger>
                    </TabsList>

                    <div className="mt-4 max-h-[60vh] overflow-y-auto">
                      <TabsContent value="basic" className="space-y-4">
                        <div>
                          <Label htmlFor="node-name">Node Name</Label>
                          <Input
                            id="node-name"
                            value={localData.name || ""}
                            onChange={(e) => handleNameChange(e.target.value)}
                            placeholder="Enter node name"
                            className="mt-1"
                          />
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            id="is-start"
                            checked={localData.isStart || false}
                            onCheckedChange={handleStartToggle}
                          />
                          <Label htmlFor="is-start">Start Node</Label>
                        </div>

                        <div>
                          <Label htmlFor="first-message">First Message (Optional)</Label>
                          <Textarea
                            id="first-message"
                            value={localData.firstMessage || ""}
                            onChange={(e) => handleFirstMessageChange(e.target.value)}
                            placeholder="Message to speak when entering this node"
                            rows={3}
                            className="mt-1"
                          />
                        </div>

                        <div>
                          <Label htmlFor="prompt">Conversation Prompt</Label>
                          <Textarea
                            id="prompt"
                            value={localData.prompt || ""}
                            onChange={(e) => handlePromptChange(e.target.value)}
                            placeholder="Enter the conversation prompt that guides the AI's behavior"
                            rows={6}
                            className="mt-1"
                          />
                        </div>
                      </TabsContent>

                      <TabsContent value="variables" className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-sm font-semibold">Variable Extraction</h3>
                            <p className="text-xs text-gray-500">Extract variables from user conversation</p>
                          </div>
                          <Button onClick={addVariable} size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Variable
                          </Button>
                        </div>

                        {localData.variables && localData.variables.length > 0 ? (
                          <div className="space-y-3">
                            {localData.variables.map((variable, index) => (
                              <div key={index} className="border border-gray-200 rounded-lg p-3">
                                <div className="flex items-center justify-between mb-3">
                                  <h4 className="text-sm font-medium">Variable {index + 1}</h4>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeVariable(index)}
                                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>

                                <div className="grid grid-cols-2 gap-3">
                                  <div>
                                    <Label className="text-xs">Name</Label>
                                    <Input
                                      value={variable.name}
                                      onChange={(e) => updateVariable(index, { name: e.target.value })}
                                      placeholder="variable_name"
                                      className="mt-1"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-xs">Type</Label>
                                    <Select
                                      value={variable.type}
                                      onValueChange={(value: any) => updateVariable(index, { type: value })}
                                    >
                                      <SelectTrigger className="mt-1">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="string">String</SelectItem>
                                        <SelectItem value="number">Number</SelectItem>
                                        <SelectItem value="boolean">Boolean</SelectItem>
                                        <SelectItem value="integer">Integer</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>

                                <div className="mt-3">
                                  <Label className="text-xs">Description</Label>
                                  <Input
                                    value={variable.description}
                                    onChange={(e) => updateVariable(index, { description: e.target.value })}
                                    placeholder="Describe what this variable represents"
                                    className="mt-1"
                                  />
                                </div>

                                <div className="flex items-center space-x-2 mt-3">
                                  <Switch
                                    checked={variable.required || false}
                                    onCheckedChange={(checked) => updateVariable(index, { required: checked })}
                                  />
                                  <Label className="text-xs">Required</Label>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <Variable className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-sm">No variables configured</p>
                            <p className="text-xs">Add variables to extract data from conversations</p>
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="advanced" className="space-y-4">
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Model Configuration</h3>
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs">Provider</Label>
                              <Select>
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select provider" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="openai">OpenAI</SelectItem>
                                  <SelectItem value="anthropic">Anthropic</SelectItem>
                                  <SelectItem value="groq">Groq</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label className="text-xs">Model</Label>
                              <Select>
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select model" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                                  <SelectItem value="claude-3">Claude 3</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-sm font-semibold mb-3">Voice Configuration</h3>
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs">Provider</Label>
                              <Select>
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select provider" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                                  <SelectItem value="openai">OpenAI</SelectItem>
                                  <SelectItem value="azure">Azure</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label className="text-xs">Voice</Label>
                              <Select>
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select voice" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="alloy">Alloy</SelectItem>
                                  <SelectItem value="echo">Echo</SelectItem>
                                  <SelectItem value="nova">Nova</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="global" className="space-y-4">
                        <div className="flex items-center space-x-2 mb-4">
                          <Switch
                            id="is-global"
                            checked={localData.globalNodePlan?.enabled || false}
                            onCheckedChange={handleGlobalToggle}
                          />
                          <div>
                            <Label htmlFor="is-global" className="text-sm font-medium">Global Node</Label>
                            <p className="text-xs text-gray-500">Allow access to this node from anywhere in the workflow</p>
                          </div>
                        </div>

                        {localData.globalNodePlan?.enabled && (
                          <div>
                            <Label htmlFor="global-condition">Enter Condition</Label>
                            <Input
                              id="global-condition"
                              value={localData.globalNodePlan?.enterCondition || ""}
                              onChange={(e) => handleGlobalConditionChange(e.target.value)}
                              placeholder="e.g., User wants to speak to a human"
                              className="mt-1"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Describe when users should be routed to this node
                            </p>
                          </div>
                        )}
                      </TabsContent>
                    </div>
                  </Tabs>
                </DialogContent>
              </Dialog>

              {!localData.isStart && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-700 rounded-md"
                  onClick={(e) => {
                    console.log('Delete button clicked');
                    e.stopPropagation();
                    e.preventDefault();
                    if (confirm('Are you sure you want to delete this node?')) {
                      localData.onDelete();
                    }
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3">
            {localData.firstMessage && (
              <div className="text-xs text-blue-700 bg-blue-50 p-2 rounded-md border border-blue-100">
                <div className="font-medium mb-1">First Message</div>
                <div className="text-blue-600">
                  {localData.firstMessage.length > 60
                    ? `${localData.firstMessage.substring(0, 60)}...`
                    : localData.firstMessage
                  }
                </div>
              </div>
            )}

            <div className="text-xs">
              <div className="font-medium text-gray-900 mb-1">Prompt</div>
              <div className="text-gray-600 leading-relaxed">
                {localData.prompt?.length > 100
                  ? `${localData.prompt.substring(0, 100)}...`
                  : localData.prompt || "No prompt configured"
                }
              </div>
            </div>

            {localData.variables && localData.variables.length > 0 && (
              <div className="text-xs">
                <div className="font-medium text-gray-900 mb-1">Variables</div>
                <div className="flex flex-wrap gap-1">
                  {localData.variables.slice(0, 3).map((variable, index) => (
                    <Badge key={index} variant="outline" className="text-xs border-purple-200 text-purple-700">
                      {variable.name}
                    </Badge>
                  ))}
                  {localData.variables.length > 3 && (
                    <Badge variant="outline" className="text-xs border-gray-200 text-gray-500">
                      +{localData.variables.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {localData.globalNodePlan?.enabled && (
              <div className="text-xs text-orange-700 bg-orange-50 p-2 rounded-md border border-orange-100">
                <div className="font-medium mb-1">Global Condition</div>
                <div className="text-orange-600">
                  {localData.globalNodePlan.enterCondition || "No condition set"}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
    </>
  );
};

export default ConversationNode;
