import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Zap,
  Settings,
  Trash2,
  Globe,
  Code,
  Key,
  Plus,
  X,
} from "lucide-react";

interface ApiRequestNodeData {
  name: string;
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
  timeoutSeconds?: number;

  // VAPI-specific configuration
  apiRequestConfig?: {
    method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    url: string;
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
  };

  // Response handling
  responseMapping?: {
    successPath?: string;
    errorPath?: string;
    variables?: Array<{
      name: string;
      path: string;
    }>;
  };

  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ApiRequestNode = ({ data, selected }: NodeProps<ApiRequestNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);
  const [activeTab, setActiveTab] = useState("basic");
  const [headersText, setHeadersText] = useState(
    JSON.stringify(data.headers || {}, null, 2)
  );
  const [bodyText, setBodyText] = useState(
    JSON.stringify(data.body || {}, null, 2)
  );

  const handleUpdate = useCallback((updates: Partial<ApiRequestNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleUrlChange = (value: string) => {
    handleUpdate({ url: value });
  };

  const handleMethodChange = (value: string) => {
    handleUpdate({ method: value as ApiRequestNodeData["method"] });
  };

  const handleTimeoutChange = (value: string) => {
    const timeout = parseInt(value) || 20;
    handleUpdate({ timeoutSeconds: timeout });
  };

  const handleHeadersChange = (value: string) => {
    setHeadersText(value);
    try {
      const headers = JSON.parse(value);
      handleUpdate({ headers });
    } catch (error) {
      // Invalid JSON, don't update
    }
  };

  const handleBodyChange = (value: string) => {
    setBodyText(value);
    try {
      const body = JSON.parse(value);
      handleUpdate({ body });
    } catch (error) {
      // Invalid JSON, don't update
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case "GET":
        return "bg-green-100 text-green-800";
      case "POST":
        return "bg-blue-100 text-blue-800";
      case "PUT":
        return "bg-yellow-100 text-yellow-800";
      case "DELETE":
        return "bg-red-100 text-red-800";
      case "PATCH":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-purple-500 border-2 border-white"
      />

      <Card
        className={`min-w-[300px] max-w-[350px] transition-all duration-200 bg-white border-2 ${
          selected
            ? "border-purple-500 shadow-lg"
            : "border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md"
        }`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Zap className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-semibold text-gray-900">
                  {localData.name || "API Request"}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={`text-xs ${getMethodColor(localData.method || "GET")}`}>
                    {localData.method || "GET"}
                  </Badge>
                  {localData.url && (
                    <Badge variant="outline" className="text-xs border-gray-200 text-gray-600">
                      <Globe className="h-3 w-3 mr-1" />
                      API
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure API Request Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div className="grid grid-cols-4 gap-4">
                      <div className="col-span-1">
                        <Label htmlFor="method">Method</Label>
                        <Select
                          value={localData.method || "GET"}
                          onValueChange={handleMethodChange}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="GET">GET</SelectItem>
                            <SelectItem value="POST">POST</SelectItem>
                            <SelectItem value="PUT">PUT</SelectItem>
                            <SelectItem value="DELETE">DELETE</SelectItem>
                            <SelectItem value="PATCH">PATCH</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="col-span-3">
                        <Label htmlFor="url">URL</Label>
                        <Input
                          id="url"
                          value={localData.url || ""}
                          onChange={(e) => handleUrlChange(e.target.value)}
                          placeholder="https://api.example.com/endpoint"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="timeout">Timeout (seconds)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={localData.timeoutSeconds || 20}
                        onChange={(e) => handleTimeoutChange(e.target.value)}
                        min="1"
                        max="300"
                      />
                    </div>

                    <div>
                      <Label htmlFor="headers">Headers (JSON)</Label>
                      <Textarea
                        id="headers"
                        value={headersText}
                        onChange={(e) => handleHeadersChange(e.target.value)}
                        placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
                        rows={4}
                        className="font-mono text-sm"
                      />
                    </div>

                    {(localData.method === "POST" || 
                      localData.method === "PUT" || 
                      localData.method === "PATCH") && (
                      <div>
                        <Label htmlFor="body">Request Body (JSON)</Label>
                        <Textarea
                          id="body"
                          value={bodyText}
                          onChange={(e) => handleBodyChange(e.target.value)}
                          placeholder='{"key": "value", "data": "{{variable}}"}'
                          rows={6}
                          className="font-mono text-sm"
                        />
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <Globe className="h-3 w-3 text-gray-500" />
              <span className="text-gray-700 truncate">
                {localData.url || "No URL configured"}
              </span>
            </div>
            
            {localData.timeoutSeconds && (
              <div className="text-xs text-gray-600">
                <strong>Timeout:</strong> {localData.timeoutSeconds}s
              </div>
            )}

            {Object.keys(localData.headers || {}).length > 0 && (
              <div className="text-xs text-gray-600">
                <strong>Headers:</strong> {Object.keys(localData.headers || {}).length} configured
              </div>
            )}

            {localData.body && Object.keys(localData.body).length > 0 && (
              <div className="text-xs text-gray-600">
                <strong>Body:</strong> {Object.keys(localData.body).length} fields
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500"
      />
    </>
  );
};

export default ApiRequestNode;
