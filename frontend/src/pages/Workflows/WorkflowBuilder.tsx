import { useState, useC<PERSON>back, useRef, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
// Custom VAPI Canvas imports
import VapiCanvas from './components/VapiCanvas';
import VapiNode from './components/VapiNode';
import { VapiNodeSidePanel } from './components/VapiNodeSidePanel';
import { Node, Edge } from './types/workflow';
import { toast } from "sonner";
import {
  Play,
  Save,
  ArrowLeft,
  Plus,
  Settings,
  Download,
  Upload,
  Trash2,
  Copy,
  Undo,
  Redo,
  Phone,
  Zap,
  MessageSquare,
  Globe,
  PhoneCall,
  StopCircle,
  Wrench,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  getWorkflowById,
  updateWorkflow,
  testWorkflow,
  autoSaveWorkflow,
  createVapiCall,
} from "@/api/services/workflows/workflowService";
import { VapiWorkflowService } from "@/api/services/vapi/vapiService";
import { Workflow, WorkflowNode as WorkflowNodeType, WorkflowEdge } from "@/types/api";
import { getVapiNodeTypes, VapiNodeType } from "@/api/services/vapi/nodeTypesService";

// We're now using VapiNode for all node types

// Initial node position
let nodeId = 0;
const getNodeId = () => `node_${nodeId++}`;

const WorkflowBuilder = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false);
  const [callPhoneNumber, setCallPhoneNumber] = useState("");
  const [isSidePanelOpen, setIsSidePanelOpen] = useState(false);
  const [sidePanelNode, setSidePanelNode] = useState<Node | null>(null);
  const [vapiNodeTypes, setVapiNodeTypes] = useState<VapiNodeType[]>([]);

  // Fetch workflow data
  const { data: workflowData, isLoading, error } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => {
      console.log("Fetching workflow with ID:", id);
      return getWorkflowById(id!);
    },
    enabled: !!id,
    retry: 1,
  });

  // Debug logging
  useEffect(() => {
    console.log("WorkflowBuilder - ID:", id);
    console.log("WorkflowBuilder - Loading:", isLoading);
    console.log("WorkflowBuilder - Error:", error);
    console.log("WorkflowBuilder - Data:", workflowData);
  }, [id, isLoading, error, workflowData]);

  // Auto-save mutation
  const autoSaveMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Workflow> }) =>
      autoSaveWorkflow(id, data),
    onSuccess: () => {
      setLastSaved(new Date());
      setIsAutoSaving(false);
    },
    onError: () => {
      setIsAutoSaving(false);
    },
  });

  // Save workflow mutation
  const saveMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Workflow> }) =>
      autoSaveWorkflow(id, data),
    onSuccess: () => {
      setLastSaved(new Date());
      toast.success("Workflow saved successfully");
    },
    onError: (error) => {
      console.error("Save error:", error);
      toast.error("Failed to save workflow");
    },
  });

  // Test workflow mutation
  const testMutation = useMutation({
    mutationFn: testWorkflow,
    onSuccess: () => {
      toast.success("Workflow test initiated successfully");
    },
  });

  // Define node update and delete handlers first
  const handleNodeUpdate = useCallback((nodeId: string, updatedData: any) => {
    setNodes((nds: Node[]) => {
      // If setting a node as start, remove start from all other nodes
      if (updatedData.isStart === true || updatedData.start === true) {
        return nds.map((node: Node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                ...updatedData,
                isStart: true,
                start: true
              }
            };
          } else {
            return {
              ...node,
              data: {
                ...node.data,
                isStart: false,
                start: false
              }
            };
          }
        });
      } else {
        return nds.map((node: Node) =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, ...updatedData } }
            : node
        );
      }
    });
  }, []);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds: Node[]) => nds.filter((node: Node) => node.id !== nodeId));
    setEdges((eds: Edge[]) => eds.filter((edge: Edge) => edge.source !== nodeId && edge.target !== nodeId));
  }, []);

  const handleNodeConfigure = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setSidePanelNode(node);
      setIsSidePanelOpen(true);
    }
  }, [nodes]);

  const handleSidePanelSave = useCallback((nodeData: any) => {
    if (sidePanelNode) {
      handleNodeUpdate(sidePanelNode.id, nodeData);
    }
  }, [sidePanelNode, handleNodeUpdate]);

  const handleSidePanelClose = useCallback(() => {
    setIsSidePanelOpen(false);
    setSidePanelNode(null);
  }, []);

  // Convert workflow data to React Flow format
  useEffect(() => {
    if (workflowData?.data) {
      const workflow = workflowData.data;
      
      // Convert nodes
      const flowNodes: Node[] = workflow.nodes?.map((node: WorkflowNodeType, index: number) => {
        const nodeId = node.name || `node-${index}`;
        return {
          id: nodeId,
          type: node.type,
          position: node.position || { x: index * 250 + 100, y: index * 150 + 100 },
          draggable: true,
          selectable: true,
          data: {
            ...node,
            onUpdate: (updatedData: any) => handleNodeUpdate(nodeId, updatedData),
            onDelete: () => handleNodeDelete(nodeId),
            onConfigure: () => handleNodeConfigure(nodeId),
          },
        };
      }) || [];

      // Convert edges
      const flowEdges: Edge[] = workflow.edges?.map((edge: WorkflowEdge, index: number) => ({
        id: `edge-${index}`,
        source: edge.from,
        target: edge.to,
        label: edge.condition?.prompt || edge.condition?.expression || "",
      })) || [];

      setNodes(flowNodes);
      setEdges(flowEdges);
      nodeId = Math.max(nodeId, flowNodes.length);

      // If no nodes exist, create a default conversation node
      if (flowNodes.length === 0) {
        const defaultNodeId = getNodeId();
        const defaultNode: Node = {
          id: defaultNodeId,
          type: 'conversation',
          position: { x: 250, y: 150 },
          data: {
            type: 'conversation',
            name: 'Start Conversation',
            prompt: 'You are a helpful AI assistant. Respond naturally and helpfully to user queries.',
            firstMessage: 'Hello! How can I help you today?',
            isStart: true,
            start: true, // Explicitly mark as start node for VAPI
            onUpdate: (updatedData: any) => handleNodeUpdate(defaultNodeId, updatedData),
            onDelete: () => handleNodeDelete(defaultNodeId),
            onConfigure: () => handleNodeConfigure(defaultNodeId),
          },
        };
        setNodes([defaultNode]);
      }
    }
  }, [workflowData, setNodes, setEdges, handleNodeUpdate, handleNodeDelete, handleNodeConfigure]);

  // Auto-save when nodes or edges change (temporarily disabled)
  useEffect(() => {
    // Temporarily disabled to prevent API validation errors during testing
    // if (nodes.length > 0 && id && !isAutoSaving) {
    //   const timeoutId = setTimeout(() => {
    //     handleAutoSave();
    //   }, 5000); // Auto-save after 5 seconds of inactivity

    //   return () => clearTimeout(timeoutId);
    // }
  }, [nodes, edges, id, isAutoSaving]);

  // Fetch VAPI node types
  useEffect(() => {
    const fetchNodeTypes = async () => {
      try {
        const nodeTypes = await getVapiNodeTypes();
        setVapiNodeTypes(nodeTypes);
      } catch (error) {
        console.error('Failed to fetch VAPI node types:', error);
        toast.error('Failed to load node types');
      }
    };

    fetchNodeTypes();
  }, []);

  // Debug: Add beforeunload listener to catch page reloads
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      console.log('Page is about to reload/unload!');
      console.trace('Reload triggered from:');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, []);

  const handleAutoSave = useCallback(() => {
    if (!id || isAutoSaving) return;

    setIsAutoSaving(true);
    
    // Convert React Flow data back to workflow format
    const workflowNodes: WorkflowNodeType[] = nodes.map((node, index) => {
      // Base node properties that are common to all node types
      const baseNode = {
        type: node.type as any,
        name: node.id,
        // VAPI expects isStart property for the start node (first node)
        isStart: index === 0 || node.data.isStart === true,
      };

      // Add type-specific properties based on VAPI API requirements
      switch (node.type) {
        case 'conversation':
          const conversationNode: any = {
            ...baseNode,
            prompt: node.data.prompt || "You are a helpful AI assistant.",
          };

          // Add variables if they exist
          if (node.data.variables && node.data.variables.length > 0) {
            conversationNode.variables = node.data.variables;
          }

          // Add global node plan if enabled
          if (node.data.globalNodePlan?.enabled) {
            conversationNode.globalNodePlan = node.data.globalNodePlan;
          }

          return conversationNode;

        case 'apiRequest':
          return {
            ...baseNode,
            method: node.data.method || 'GET',
            url: node.data.url || '',
            headers: node.data.headers || {},
            body: node.data.body || {},
            timeoutSeconds: node.data.timeoutSeconds || 20,
          };

        case 'transferCall':
          return {
            ...baseNode,
            destination: node.data.destination || '',
            transferPlan: {
              destination: node.data.destination || '',
              message: node.data.transferMessage || 'Please hold while I transfer you.',
              summary: node.data.summary || '',
            },
          };

        case 'endCall':
          return {
            ...baseNode,
          };

        case 'tool':
          return {
            ...baseNode,
            toolIds: node.data.toolIds || [],
            tools: node.data.tools || [],
          };

        default:
          // For unknown node types, only include basic properties
          return {
            ...baseNode,
            prompt: node.data.prompt || "Default prompt",
          };
      }
    });

    const workflowEdges: WorkflowEdge[] = edges.map((edge) => ({
      from: edge.source,
      to: edge.target,
      condition: edge.label ? {
        type: "ai" as const,
        prompt: edge.label as string,
      } : undefined,
    }));

    // Debug: Log what we're sending to the API
    console.log("Sending to VAPI API:", {
      nodes: workflowNodes,
      edges: workflowEdges,
    });

    autoSaveMutation.mutate({
      id,
      data: {
        nodes: workflowNodes,
        edges: workflowEdges,
      },
    });
  }, [id, nodes, edges, isAutoSaving, autoSaveMutation]);

  const onConnect = useCallback((sourceId: string, targetId: string) => {
    console.log('onConnect called with:', sourceId, '->', targetId);

    // Check if connection already exists
    const existingEdge = edges.find(edge =>
      edge.source === sourceId && edge.target === targetId
    );

    if (existingEdge) {
      console.log('Connection already exists, skipping');
      return;
    }

    const newEdge: Edge = {
      id: `edge-${Date.now()}`, // Use timestamp for unique ID
      source: sourceId,
      target: targetId,
    };

    console.log('Creating new edge:', newEdge);
    setEdges((eds: Edge[]) => {
      const updatedEdges = [...eds, newEdge];
      console.log('Updated edges:', updatedEdges);
      return updatedEdges;
    });

    toast.success('Nodes connected successfully');
  }, [edges]);

  const onNodeClick = useCallback((node: Node) => {
    console.log('Node clicked:', node.id, node.type);
    setSelectedNode(node);
  }, []);

  const handleNodesChange = useCallback((newNodes: Node[]) => {
    console.log('Nodes changing:', newNodes);
    setNodes(newNodes);
  }, []);

  const handleEdgesChange = useCallback((newEdges: Edge[]) => {
    setEdges(newEdges);
  }, []);

  // Simple add node function for custom canvas
  const addNode = useCallback((type: string, position?: { x: number; y: number }) => {
    const nodeId = getNodeId();
    const nodePosition = position || { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 };
    console.log('Adding node:', nodeId, 'type:', type, 'at position:', nodePosition);

    const newNode: Node = {
      id: nodeId,
      type,
      position: nodePosition,
      data: {
        type,
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Node`,
        prompt: type === 'conversation' ? 'You are a helpful AI assistant. Respond naturally and helpfully to user queries.' : '',
        firstMessage: type === 'conversation' ? 'Hello! How can I help you today?' : '',
        isStart: nodes.length === 0, // First node is start node
        start: nodes.length === 0, // Explicitly mark as start node for VAPI
        onUpdate: (updatedData: any) => handleNodeUpdate(nodeId, updatedData),
        onDelete: () => handleNodeDelete(nodeId),
        onConfigure: () => handleNodeConfigure(nodeId),
        // Initialize type-specific data
        ...(type === 'apiRequest' && {
          method: 'GET',
          url: '',
          headers: {},
          body: {},
        }),
        ...(type === 'transferCall' && {
          destination: '',
          transferPlan: {
            message: '',
            summary: '',
          },
        }),
        ...(type === 'tool' && {
          toolIds: [],
          tools: [],
        }),
      },
    };

    setNodes((nds: Node[]) => [...nds, newNode]);
  }, [nodes.length, handleNodeUpdate, handleNodeDelete, handleNodeConfigure]);



  const handleTestWorkflow = () => {
    if (id) {
      testMutation.mutate(id);
    }
  };

  const handleSave = () => {
    if (!id || nodes.length === 0) {
      toast.error("No workflow to save");
      return;
    }

    // Convert nodes to workflow format (exclude position as VAPI doesn't expect it)
    const workflowNodes = nodes.map((node, index) => {
      // Base node properties that are common to all node types
      const baseNode = {
        type: node.type as any,
        name: node.id,
        // VAPI expects isStart property for the start node (first node)
        isStart: index === 0 || node.data.isStart === true,
      };

      // Add type-specific properties based on VAPI API requirements
      switch (node.type) {
        case 'conversation':
          const conversationNode: any = {
            ...baseNode,
            prompt: node.data.prompt || "You are a helpful AI assistant.",
          };

          // Add variables if they exist
          if (node.data.variables && node.data.variables.length > 0) {
            conversationNode.variables = node.data.variables;
          }

          return conversationNode;

        case 'apiRequest':
          return {
            ...baseNode,
            method: node.data.method || 'GET',
            url: node.data.url || '',
            headers: node.data.headers || {},
            body: node.data.body || {},
            timeoutSeconds: node.data.timeoutSeconds || 20,
          };

        case 'transferCall':
          return {
            ...baseNode,
            destination: node.data.destination || '',
            transferPlan: {
              destination: node.data.destination || '',
              message: node.data.transferMessage || 'Please hold while I transfer you.',
              summary: node.data.summary || '',
            },
          };

        case 'endCall':
          return {
            ...baseNode,
          };

        case 'tool':
          return {
            ...baseNode,
            toolIds: node.data.toolIds || [],
            tools: node.data.tools || [],
          };

        default:
          // For unknown node types, only include basic properties and filter out frontend-specific ones
          const allowedProps = ['prompt', 'variables', 'globalNodePlan'];
          const filteredData = Object.fromEntries(
            Object.entries(node.data).filter(([key]) =>
              allowedProps.includes(key)
            )
          );
          return {
            ...baseNode,
            prompt: node.data.prompt || "Default prompt",
            ...filteredData,
          };
      }
    });

    // Convert edges to workflow format
    const workflowEdges = edges.map((edge) => ({
      from: edge.source,
      to: edge.target,
      condition: edge.label ? {
        type: 'ai' as const,
        prompt: edge.label
      } : undefined,
    }));

    console.log("Saving workflow with data:", {
      nodes: workflowNodes,
      edges: workflowEdges,
    });

    // Validate that no forbidden properties exist in nodes
    const forbiddenProps = ['position', 'start', 'firstMessage']; // These properties are not allowed in VAPI workflow nodes
    const hasForbiddenProps = workflowNodes.some(node =>
      forbiddenProps.some(prop => prop in node)
    );
    if (hasForbiddenProps) {
      console.error("ERROR: Found forbidden properties in nodes!", workflowNodes);
      toast.error("Internal error: Forbidden properties found in nodes");
      return;
    }

    // Validate that exactly one node has isStart: true
    const startNodes = workflowNodes.filter(node => node.isStart === true);
    if (startNodes.length !== 1) {
      console.error("ERROR: Must have exactly one start node!", { startNodes, workflowNodes });
      toast.error("Workflow must have exactly one start node");
      return;
    }

    // Log the exact structure being sent to VAPI
    console.log("Final workflow data being sent to VAPI:", JSON.stringify({
      nodes: workflowNodes,
      edges: workflowEdges,
    }, null, 2));

    // Additional validation: Check each node structure
    workflowNodes.forEach((node, index) => {
      console.log(`Node ${index} (${node.name}):`, {
        type: node.type,
        isStart: node.isStart,
        hasStart: 'start' in node,
        hasPosition: 'position' in node,
        keys: Object.keys(node)
      });
    });

    // Use the save mutation
    saveMutation.mutate({
      id,
      data: {
        nodes: workflowNodes,
        edges: workflowEdges,
      },
    });
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <h2 className="text-xl font-semibold mb-2">Error Loading Workflow</h2>
            <p className="text-gray-600 mb-4">
              {error instanceof Error ? error.message : "Failed to load workflow"}
            </p>
            <p className="text-sm text-gray-500 mb-4">Workflow ID: {id}</p>
          </div>
          <div className="space-x-2">
            <Button onClick={() => navigate("/workflows")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleCallWorkflow = async () => {
    if (!callPhoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    try {
      await createVapiCall({
        workflowId: id!,
        customer: {
          number: callPhoneNumber,
        },
        metadata: {
          testCall: true,
          timestamp: new Date().toISOString(),
        },
      });
      setIsCallDialogOpen(false);
      setCallPhoneNumber("");
    } catch (error) {
      console.error("Error creating call:", error);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* VAPI-style Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/workflows")}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {workflowData?.data?.name || "Untitled Workflow"}
              </h1>
              <div className="flex items-center gap-3 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  {isAutoSaving ? (
                    <>
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                      Auto-saving...
                    </>
                  ) : lastSaved ? (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      Saved {lastSaved.toLocaleTimeString()}
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-gray-400 rounded-full" />
                      Not saved
                    </>
                  )}
                </span>
                <div className="h-3 w-px bg-gray-300" />
                <Badge variant="secondary" className="text-xs">
                  {nodes.length} nodes
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {edges.length} connections
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">


            <div className="h-6 w-px bg-gray-300" />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSave}
                    disabled={isAutoSaving || saveMutation.isPending}
                    className="text-gray-600"
                  >
                    {saveMutation.isPending ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Save Workflow</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Test Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                console.log('Test button clicked - adding conversation node');
                addNode('conversation');
              }}
            >
              + Test Node
            </Button>

            <Dialog open={isCallDialogOpen} onOpenChange={setIsCallDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Test Workflow with Call</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="phone-number">Phone Number</Label>
                    <Input
                      id="phone-number"
                      value={callPhoneNumber}
                      onChange={(e) => setCallPhoneNumber(e.target.value)}
                      placeholder="+1234567890"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsCallDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleCallWorkflow}>
                      <Phone className="h-4 w-4 mr-2" />
                      Start Call
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        console.log('🔍 Debug Info:');
                        console.log('- Sidebar open:', isSidePanelOpen);
                        console.log('- Nodes count:', nodes.length);
                        console.log('- Selected node:', selectedNode?.id);
                        console.log('- Side panel node:', sidePanelNode?.id);

                        // Add a test node if none exist
                        if (nodes.length === 0) {
                          console.log('🎯 Adding test node for drag testing');
                          const testNode = {
                            id: 'test-node-1',
                            type: 'conversation',
                            position: { x: 200, y: 200 },
                            draggable: true,
                            selectable: true,
                            data: {
                              name: 'Test Node',
                              type: 'conversation',
                              isStart: true,
                              onUpdate: (data: any) => console.log('Update:', data),
                              onDelete: () => console.log('Delete'),
                              onConfigure: () => {
                                console.log('Configure test node');
                                setSidePanelNode({
                                  id: 'test-node-1',
                                  type: 'conversation',
                                  position: { x: 200, y: 200 },
                                  data: { name: 'Test Node', type: 'conversation' }
                                });
                                setIsSidePanelOpen(true);
                              },
                            },
                          };
                          setNodes([testNode]);
                        }
                      }}
                    >
                      Debug & Test
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* VAPI-style Sidebar - Node Palette */}
        <div className={`${sidebarCollapsed ? 'w-16' : 'w-72'} bg-gray-50 border-r border-gray-200 flex-shrink-0 transition-all duration-200`}>
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              {!sidebarCollapsed && (
                <h3 className="font-semibold text-gray-900">Add Nodes</h3>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="text-gray-600 hover:text-gray-900"
              >
                {sidebarCollapsed ? <Plus className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
              </Button>
            </div>

            <div className="space-y-2">
              {vapiNodeTypes.map((nodeType) => {
                // Map icon names to actual icon components
                const getIconComponent = (iconName: string) => {
                  switch (iconName) {
                    case 'MessageCircle':
                      return MessageSquare;
                    case 'Zap':
                      return Zap;
                    case 'PhoneCall':
                      return PhoneCall;
                    case 'StopCircle':
                      return StopCircle;
                    case 'Wrench':
                      return Wrench;
                    default:
                      return MessageSquare;
                  }
                };

                const IconComponent = getIconComponent(nodeType.icon);
                return (
                  <TooltipProvider key={nodeType.type}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={`flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-white hover:shadow-sm transition-all duration-200 bg-white ${sidebarCollapsed ? 'justify-center' : ''}`}
                          draggable
                          onDragStart={(event) => {
                            event.dataTransfer.setData("application/reactflow", nodeType.type);
                            event.dataTransfer.effectAllowed = "move";
                          }}
                          onClick={() => addNode(nodeType.type)}
                        >
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${nodeType.color}`}>
                            <IconComponent className="h-4 w-4" />
                          </div>
                          {!sidebarCollapsed && (
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">{nodeType.label}</div>
                              <div className="text-xs text-gray-500">{nodeType.description}</div>
                            </div>
                          )}
                        </div>
                      </TooltipTrigger>
                      {sidebarCollapsed && (
                        <TooltipContent side="right">
                          <div>
                            <div className="font-medium">{nodeType.label}</div>
                            <div className="text-xs text-gray-500">{nodeType.description}</div>
                          </div>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </div>
          </div>
        </div>

        {/* Custom VAPI Canvas */}
        <div className="flex-1 relative">
          <VapiCanvas
            nodes={nodes}
            edges={edges}
            onNodesChange={handleNodesChange}
            onEdgesChange={handleEdgesChange}
            onNodeClick={onNodeClick}
            onConnect={onConnect}
            selectedNode={selectedNode}
            nodeTypes={{
              conversation: VapiNode,
              apiRequest: VapiNode,
              transferCall: VapiNode,
              endCall: VapiNode,
              tool: VapiNode
            }}
          />

          {/* Empty State */}
          {nodes.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Building Your Workflow</h3>
                <p className="text-gray-500 mb-4 max-w-sm">
                  Drag nodes from the sidebar or click on them to add to your workflow
                </p>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
                  <span>Tip: Start with a</span>
                  <Badge variant="secondary" className="text-xs">Conversation</Badge>
                  <span>node</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Side Panel for Node Configuration */}
      <VapiNodeSidePanel
        isOpen={isSidePanelOpen}
        onClose={handleSidePanelClose}
        node={sidePanelNode}
        onSave={handleSidePanelSave}
      />
    </div>
  );
};

export default WorkflowBuilder;
