import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { getNodeTypeConfiguration, VapiNodeType } from '@/api/services/vapi/nodeTypesService';
import { Node } from '../types/workflow';

interface VapiNodeConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  node: Node | null;
  onSave: (nodeData: any) => void;
}

export const VapiNodeConfigModal: React.FC<VapiNodeConfigModalProps> = ({
  isOpen,
  onClose,
  node,
  onSave,
}) => {
  const [nodeConfig, setNodeConfig] = useState<VapiNodeType | null>(null);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (node && isOpen) {
      console.log('Modal opened for node:', node);
      loadNodeConfiguration();
    }
  }, [node, isOpen]);

  const loadNodeConfiguration = async () => {
    if (!node) return;
    
    setLoading(true);
    try {
      const config = await getNodeTypeConfiguration(node.type);
      setNodeConfig(config);
      
      // Initialize form data with node's current data or default values
      const initialData: Record<string, any> = {};
      config?.configuration.fields.forEach(field => {
        initialData[field.name] = node.data[field.name] ?? field.defaultValue ?? '';
      });
      setFormData(initialData);
    } catch (error) {
      console.error('Failed to load node configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = useCallback((fieldName: string, value: any) => {
    console.log('Field change:', fieldName, value);
    setFormData(prev => {
      const newData = { ...prev, [fieldName]: value };
      console.log('Updated form data:', newData);
      return newData;
    });
  }, []);

  const handleSave = useCallback((e?: React.MouseEvent | React.FormEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    console.log('Saving node configuration:', formData);
    onSave(formData);
    onClose();
  }, [formData, onSave, onClose]);

  const renderField = (field: any) => {
    const value = formData[field.name] || '';

    switch (field.type) {
      case 'text':
        return (
          <div key={field.name} className="space-y-3">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              placeholder={field.description}
              className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            {field.description && (
              <p className="text-xs text-gray-500 mt-1">{field.description}</p>
            )}
          </div>
        );

      case 'textarea':
        return (
          <div key={field.name} className="space-y-3">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                  e.preventDefault();
                  handleSave();
                }
              }}
              placeholder={field.description}
              className="w-full min-h-[120px] border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none"
            />
            {field.description && (
              <p className="text-xs text-gray-500 mt-1">
                {field.description}
                <span className="text-gray-400 ml-2">Ctrl+Enter to save</span>
              </p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select value={value} onValueChange={(val) => handleFieldChange(field.name, val)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={field.description} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      case 'number':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="number"
              value={value}
              onChange={(e) => handleFieldChange(field.name, parseFloat(e.target.value) || 0)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              placeholder={field.description}
              className="w-full"
            />
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      case 'boolean':
        return (
          <div key={field.name} className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id={field.name}
                checked={value}
                onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
              />
              <Label htmlFor={field.name} className="text-sm font-medium">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
            </div>
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  if (!node) return null;

  const handleClose = useCallback(() => {
    console.log('Closing modal');
    onClose();
  }, [onClose]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        console.log('Dialog open change:', open);
        if (!open) {
          handleClose();
        }
      }}
    >
      <DialogContent
        className="max-w-2xl max-h-[80vh] overflow-y-auto"
        onPointerDownOutside={(e) => {
          console.log('Pointer down outside - preventing close');
          e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          console.log('Escape key down');
          e.preventDefault();
          handleClose();
        }}
      >
        <DialogHeader className="border-b pb-4">
          <DialogTitle className="flex items-center gap-2 text-lg font-semibold">
            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${nodeConfig?.color || 'bg-gray-100'}`}>
              <span className="text-sm font-medium">
                {nodeConfig?.label?.charAt(0) || node?.type?.charAt(0).toUpperCase()}
              </span>
            </div>
            Configure {nodeConfig?.label || node?.type} Node
          </DialogTitle>
          {nodeConfig?.description && (
            <p className="text-sm text-gray-600 mt-2">{nodeConfig.description}</p>
          )}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : nodeConfig ? (
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSave();
            }}
            className="space-y-6 py-4"
          >
            <div className="space-y-6">
              {nodeConfig.configuration.fields.map(renderField)}
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleClose();
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700"
              >
                Save Configuration
              </Button>
            </div>
          </form>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No configuration available for this node type.
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
