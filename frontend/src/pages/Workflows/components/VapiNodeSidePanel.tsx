import React, { useState, useEffect, useCallback } from 'react';
import { X, Settings, MessageCircle, Globe, Variable, Star } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { getNodeTypeConfiguration, VapiNodeType } from '@/api/services/vapi/nodeTypesService';
import { Node } from '../types/workflow';

interface VapiNodeSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  node: Node | null;
  onSave: (nodeData: any) => void;
}

export const VapiNodeSidePanel: React.FC<VapiNodeSidePanelProps> = ({
  isOpen,
  onClose,
  node,
  onSave,
}) => {
  const [nodeConfig, setNodeConfig] = useState<VapiNodeType | null>(null);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (node && isOpen) {
      console.log('Side panel opened for node:', node);
      loadNodeConfiguration();
    }
  }, [node, isOpen]);

  const loadNodeConfiguration = async () => {
    if (!node) return;
    
    setLoading(true);
    try {
      const config = await getNodeTypeConfiguration(node.type);
      setNodeConfig(config);
      
      // Initialize form data with existing node data
      const initialData = { ...node.data };
      setFormData(initialData);
      console.log('Loaded node config:', config);
      console.log('Initial form data:', initialData);
    } catch (error) {
      console.error('Failed to load node configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = useCallback((fieldName: string, value: any) => {
    console.log('Field change:', fieldName, value);
    setFormData(prev => {
      const newData = { ...prev, [fieldName]: value };
      console.log('Updated form data:', newData);
      return newData;
    });
  }, []);

  const handleSave = useCallback(() => {
    console.log('Saving node configuration:', formData);
    onSave(formData);
  }, [formData, onSave]);

  const handleClose = useCallback(() => {
    console.log('Closing side panel');
    onClose();
  }, [onClose]);

  const getNodeIcon = () => {
    switch (node?.type) {
      case 'conversation':
        return <MessageCircle className="h-5 w-5 text-blue-600" />;
      case 'apiRequest':
        return <div className="h-5 w-5 bg-purple-600 rounded" />;
      case 'transferCall':
        return <div className="h-5 w-5 bg-green-600 rounded" />;
      case 'endCall':
        return <div className="h-5 w-5 bg-red-600 rounded" />;
      case 'tool':
        return <div className="h-5 w-5 bg-orange-600 rounded" />;
      default:
        return <MessageCircle className="h-5 w-5 text-blue-600" />;
    }
  };

  const getNodeTypeLabel = () => {
    switch (node?.type) {
      case 'conversation':
        return 'Conversation Node';
      case 'apiRequest':
        return 'API Request Node';
      case 'transferCall':
        return 'Transfer Call Node';
      case 'endCall':
        return 'End Call Node';
      case 'tool':
        return 'Tool Node';
      default:
        return 'Node';
    }
  };

  const renderField = (field: any) => {
    const value = formData[field.name] || '';

    switch (field.type) {
      case 'text':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.description}
              className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.description}
              className="w-full min-h-[120px] border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none"
            />
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select value={value} onValueChange={(val) => handleFieldChange(field.name, val)}>
              <SelectTrigger className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                <SelectValue placeholder={field.description} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      case 'boolean':
        return (
          <div key={field.name} className="flex items-center justify-between py-2">
            <div className="space-y-1">
              <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
                {field.label}
              </Label>
              {field.description && (
                <p className="text-xs text-gray-500">{field.description}</p>
              )}
            </div>
            <Switch
              id={field.name}
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
            />
          </div>
        );

      case 'number':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="number"
              value={value}
              onChange={(e) => handleFieldChange(field.name, parseFloat(e.target.value) || 0)}
              placeholder={field.description}
              className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  if (!isOpen) return null;

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-y-0 right-0 z-50 w-96 bg-white border-l border-gray-200 shadow-xl">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            {getNodeIcon()}
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {getNodeTypeLabel()}
              </h2>
              <p className="text-sm text-gray-500">
                Configure node settings
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : nodeConfig ? (
            <div className="space-y-6">
              {nodeConfig.configuration.fields.map(renderField)}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No configuration available for this node type.
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
