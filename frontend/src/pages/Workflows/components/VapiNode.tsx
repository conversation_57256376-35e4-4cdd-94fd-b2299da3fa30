import React, { useState } from 'react';
import { Message<PERSON>ircle, Settings, Trash2, Star, Globe, Variable, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { VapiNodeConfigModal } from './VapiNodeConfigModal';

interface VapiNodeProps {
  data: {
    name?: string;
    type: string;
    prompt?: string;
    firstMessage?: string;
    isStart?: boolean;
    variables?: any[];
    globalNodePlan?: { enabled: boolean };
    onUpdate: (data: any) => void;
    onDelete: () => void;
    onConfigure?: () => void; // New prop for opening side panel
  };
  selected: boolean;
}

const VapiNode: React.FC<VapiNodeProps> = ({ data, selected }) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = (updates: any) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  };

  const getNodeIcon = () => {
    switch (data.type) {
      case 'conversation':
        return <MessageCircle className="h-4 w-4 text-blue-600" />;
      case 'apiRequest':
        return <div className="h-4 w-4 bg-purple-600 rounded" />;
      case 'transferCall':
        return <div className="h-4 w-4 bg-green-600 rounded" />;
      case 'endCall':
        return <div className="h-4 w-4 bg-red-600 rounded" />;
      case 'tool':
        return <div className="h-4 w-4 bg-orange-600 rounded" />;
      default:
        return <MessageCircle className="h-4 w-4 text-blue-600" />;
    }
  };

  const getNodeColor = () => {
    if (data.isStart) return 'border-green-500 bg-green-50';
    if (selected) return 'border-blue-500 shadow-lg';
    return 'border-gray-300 hover:border-gray-400';
  };

  return (
    <div
      className={`w-[280px] bg-white border-2 rounded-lg shadow-sm transition-all duration-200 cursor-move hover:shadow-md ${getNodeColor()}`}
      draggable={false}
      title="Click and drag to move this node"
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <GripVertical className="h-4 w-4 text-gray-400 cursor-move" title="Drag to move node" />
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              {getNodeIcon()}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">
                {localData.name || 'Conversation'}
              </div>
              <div className="flex items-center gap-1 mt-1">
                {localData.isStart && (
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                    <Star className="h-3 w-3 mr-1" />
                    Start
                  </Badge>
                )}
                {localData.globalNodePlan?.enabled && (
                  <Badge variant="outline" className="text-xs border-orange-200 text-orange-700">
                    <Globe className="h-3 w-3 mr-1" />
                    Global
                  </Badge>
                )}
                {localData.variables && localData.variables.length > 0 && (
                  <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                    <Variable className="h-3 w-3 mr-1" />
                    {localData.variables.length}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-gray-100 rounded-md"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                if (data.onConfigure) {
                  data.onConfigure();
                } else {
                  setIsConfigOpen(true); // Fallback to modal
                }
              }}
            >
              <Settings className="h-4 w-4 text-gray-500" />
            </Button>
            {!localData.isStart && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-700 rounded-md"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  if (confirm('Are you sure you want to delete this node?')) {
                    data.onDelete();
                  }
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-3 space-y-2">
        {localData.firstMessage && (
          <div className="text-xs text-blue-700 bg-blue-50 p-2 rounded border border-blue-100">
            <div className="font-medium mb-1">First Message</div>
            <div className="text-blue-600">
              {localData.firstMessage.length > 50
                ? `${localData.firstMessage.substring(0, 50)}...`
                : localData.firstMessage}
            </div>
          </div>
        )}

        <div className="text-xs">
          <div className="font-medium text-gray-900 mb-1">Prompt</div>
          <div className="text-gray-600 leading-relaxed">
            {localData.prompt?.length > 80
              ? `${localData.prompt.substring(0, 80)}...`
              : localData.prompt || 'No prompt configured'}
          </div>
        </div>

        {localData.variables && localData.variables.length > 0 && (
          <div className="text-xs">
            <div className="font-medium text-gray-900 mb-1">Variables</div>
            <div className="flex flex-wrap gap-1">
              {localData.variables.slice(0, 3).map((variable: any, index: number) => (
                <Badge key={index} variant="outline" className="text-xs border-purple-200 text-purple-700">
                  {variable.name}
                </Badge>
              ))}
              {localData.variables.length > 3 && (
                <Badge variant="outline" className="text-xs border-gray-200 text-gray-500">
                  +{localData.variables.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {localData.globalNodePlan?.enabled && (
          <div className="text-xs text-orange-700 bg-orange-50 p-2 rounded border border-orange-100">
            <div className="font-medium mb-1">Global Condition</div>
            <div className="text-orange-600">
              {localData.globalNodePlan.enterCondition || 'No condition set'}
            </div>
          </div>
        )}
      </div>

      <VapiNodeConfigModal
        isOpen={isConfigOpen}
        onClose={() => setIsConfigOpen(false)}
        node={{
          id: data.name || 'node',
          type: data.type,
          data: localData,
          position: { x: 0, y: 0 }
        }}
        onSave={handleUpdate}
      />
    </div>
  );
};

export default VapiNode;
