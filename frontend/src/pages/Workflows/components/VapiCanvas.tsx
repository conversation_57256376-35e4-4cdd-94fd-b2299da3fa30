import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Node, Edge } from '../types/workflow';

interface VapiCanvasProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (nodes: Node[]) => void;
  onEdgesChange: (edges: Edge[]) => void;
  onNodeClick: (node: Node) => void;
  onConnect: (sourceId: string, targetId: string) => void;
  selectedNode: Node | null;
  nodeTypes: Record<string, React.ComponentType<any>>;
}

interface DragState {
  isDragging: boolean;
  dragType: 'node' | 'canvas' | 'connection';
  draggedNode: Node | null;
  offset: { x: number; y: number };
  connectionStart: { nodeId: string; x: number; y: number } | null;
  startTime: number;
}

const VapiCanvas: React.FC<VapiCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onNodeClick,
  onConnect,
  selectedNode,
  nodeTypes,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragType: 'canvas',
    draggedNode: null,
    offset: { x: 0, y: 0 },
    connectionStart: null,
    startTime: 0,
  });
  const [canvasTransform, setCanvasTransform] = useState({ x: 0, y: 0, scale: 1 });
  const [tempConnection, setTempConnection] = useState<{ start: { x: number; y: number }; end: { x: number; y: number } } | null>(null);
  const [dragPreview, setDragPreview] = useState<{ nodeId: string; position: { x: number; y: number } } | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Use refs to store current values for event handlers
  const dragStateRef = useRef(dragState);
  const canvasTransformRef = useRef(canvasTransform);
  const nodesRef = useRef(nodes);
  const dragPreviewRef = useRef(dragPreview);
  const tempConnectionRef = useRef(tempConnection);

  // Update refs when values change
  useEffect(() => {
    dragStateRef.current = dragState;
  }, [dragState]);

  useEffect(() => {
    canvasTransformRef.current = canvasTransform;
  }, [canvasTransform]);

  useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);

  useEffect(() => {
    dragPreviewRef.current = dragPreview;
  }, [dragPreview]);

  useEffect(() => {
    tempConnectionRef.current = tempConnection;
  }, [tempConnection]);

  // Handle mouse down on canvas
  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      e.preventDefault();
      setDragState({
        isDragging: true,
        dragType: 'canvas',
        draggedNode: null,
        offset: { x: e.clientX - canvasTransform.x, y: e.clientY - canvasTransform.y },
        connectionStart: null,
        startTime: Date.now(),
      });
    }
  }, [canvasTransform]);

  // Handle node mouse down
  const handleNodeMouseDown = useCallback((e: React.MouseEvent, node: Node) => {
    // Prevent dragging when clicking on buttons
    const target = e.target as HTMLElement;
    if (target.closest('button') || target.closest('[role="button"]')) {
      console.log('🚫 Clicked on button, preventing drag');
      e.stopPropagation();
      e.preventDefault();
      return;
    }

    e.preventDefault();
    e.stopPropagation();
    console.log('🖱️ Node mouse down:', node.id, 'at position:', node.position);
    console.log('🔍 Current drag state before:', dragState);
    console.log('🎯 Mouse event details:', { clientX: e.clientX, clientY: e.clientY });

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    // Calculate the offset from mouse position to node's top-left corner
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // Convert to canvas coordinates
    const canvasMouseX = (mouseX - canvasTransform.x) / canvasTransform.scale;
    const canvasMouseY = (mouseY - canvasTransform.y) / canvasTransform.scale;

    console.log('📍 Mouse canvas position:', canvasMouseX, canvasMouseY);
    console.log('📍 Node position:', node.position.x, node.position.y);
    console.log('🔄 Canvas transform:', canvasTransform);

    const newDragState = {
      isDragging: true,
      dragType: 'node' as const,
      draggedNode: node,
      offset: {
        x: canvasMouseX - node.position.x,
        y: canvasMouseY - node.position.y,
      },
      connectionStart: null,
      startTime: Date.now(),
    };

    console.log('✅ Setting new drag state:', newDragState);
    setDragState(newDragState);
  }, [canvasTransform]);

  // Handle connection start
  const handleConnectionStart = useCallback((e: React.MouseEvent, nodeId: string) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Starting connection from node:', nodeId);

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const startX = (node.position.x + 140) * canvasTransform.scale + canvasTransform.x; // Center of node
    const startY = (node.position.y + 120) * canvasTransform.scale + canvasTransform.y; // Bottom of node

    setDragState({
      isDragging: true,
      dragType: 'connection',
      draggedNode: null,
      offset: { x: 0, y: 0 },
      connectionStart: { nodeId, x: startX, y: startY },
      startTime: Date.now(),
    });

    setTempConnection({
      start: { x: startX, y: startY },
      end: { x: e.clientX - rect.left, y: e.clientY - rect.top },
    });
  }, [nodes, canvasTransform]);



  // Add document-level event listeners for drag operations
  useEffect(() => {
    const handleDocumentMouseMove = (e: MouseEvent) => {
      const currentDragState = dragStateRef.current;
      const currentCanvasTransform = canvasTransformRef.current;
      const currentTempConnection = tempConnectionRef.current;

      if (!currentDragState.isDragging) return;

      console.log('🖱️ Document mouse move - drag type:', currentDragState.dragType, 'isDragging:', currentDragState.isDragging, 'mouse:', e.clientX, e.clientY);
      e.preventDefault();
      e.stopPropagation();

      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      if (currentDragState.dragType === 'canvas') {
        setCanvasTransform(prev => ({
          ...prev,
          x: e.clientX - currentDragState.offset.x,
          y: e.clientY - currentDragState.offset.y,
        }));
      } else if (currentDragState.dragType === 'node' && currentDragState.draggedNode) {
        // Calculate mouse position in canvas coordinates
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        const canvasMouseX = (mouseX - currentCanvasTransform.x) / currentCanvasTransform.scale;
        const canvasMouseY = (mouseY - currentCanvasTransform.y) / currentCanvasTransform.scale;

        // Calculate new node position by subtracting the offset
        const newX = canvasMouseX - currentDragState.offset.x;
        const newY = canvasMouseY - currentDragState.offset.y;

        // Use requestAnimationFrame for smooth drag updates
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }

        animationFrameRef.current = requestAnimationFrame(() => {
          setDragPreview({
            nodeId: currentDragState.draggedNode!.id,
            position: { x: newX, y: newY }
          });
        });
      } else if (currentDragState.dragType === 'connection' && currentTempConnection) {
        setTempConnection(prev => prev ? {
          ...prev,
          end: { x: e.clientX - rect.left, y: e.clientY - rect.top }
        } : null);
      }
    };

    const handleDocumentMouseUp = (e: MouseEvent) => {
      const currentDragState = dragStateRef.current;
      const currentCanvasTransform = canvasTransformRef.current;
      const currentNodes = nodesRef.current;
      const currentDragPreview = dragPreviewRef.current;

      if (!currentDragState.isDragging) return;

      console.log('Document mouse up - drag type:', currentDragState.dragType, 'isDragging:', currentDragState.isDragging);
      e.preventDefault();
      e.stopPropagation();

      if (currentDragState.dragType === 'connection' && currentDragState.connectionStart) {
        // Check if we're over a node
        const rect = canvasRef.current?.getBoundingClientRect();
        if (rect) {
          const x = (e.clientX - rect.left - currentCanvasTransform.x) / currentCanvasTransform.scale;
          const y = (e.clientY - rect.top - currentCanvasTransform.y) / currentCanvasTransform.scale;

          // More generous hit detection for connection
          const targetNode = currentNodes.find(node => {
            const nodeLeft = node.position.x;
            const nodeRight = node.position.x + 280;
            const nodeTop = node.position.y;
            const nodeBottom = node.position.y + 120; // Slightly larger hit area

            return x >= nodeLeft - 20 && x <= nodeRight + 20 &&
                   y >= nodeTop - 20 && y <= nodeBottom + 20 &&
                   node.id !== currentDragState.connectionStart!.nodeId;
          });

          if (targetNode) {
            onConnect(currentDragState.connectionStart.nodeId, targetNode.id);
          }
        }
      }

      // Commit drag preview to actual node position
      if (currentDragState.dragType === 'node' && currentDragState.draggedNode && currentDragPreview) {
        const updatedNodes = currentNodes.map(node =>
          node.id === currentDragState.draggedNode!.id
            ? { ...node, position: currentDragPreview.position }
            : node
        );
        onNodesChange(updatedNodes);
      }

      setDragState({
        isDragging: false,
        dragType: 'canvas',
        draggedNode: null,
        offset: { x: 0, y: 0 },
        connectionStart: null,
        startTime: 0,
      });
      setTempConnection(null);
      setDragPreview(null);

      // Clean up animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };

    if (dragState.isDragging) {
      console.log('🎧 Attaching document event listeners for drag type:', dragState.dragType);
      document.addEventListener('mousemove', handleDocumentMouseMove);
      document.addEventListener('mouseup', handleDocumentMouseUp);

      // Prevent text selection during drag
      document.body.style.userSelect = 'none';
      document.body.style.cursor = dragState.dragType === 'node' ? 'grabbing' :
                                   dragState.dragType === 'connection' ? 'crosshair' : 'grabbing';

      return () => {
        console.log('🧹 Cleaning up document event listeners');
        document.removeEventListener('mousemove', handleDocumentMouseMove);
        document.removeEventListener('mouseup', handleDocumentMouseUp);
        document.body.style.userSelect = '';
        document.body.style.cursor = '';
      };
    } else {
      console.log('❌ Not attaching event listeners - isDragging:', dragState.isDragging);
    }
  }, [dragState.isDragging, dragState.dragType, onConnect, onNodesChange]);

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);



  // Handle wheel for zooming
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setCanvasTransform(prev => ({
      ...prev,
      scale: Math.max(0.1, Math.min(3, prev.scale * delta))
    }));
  }, []);

  // Render connection lines
  const renderConnections = () => {
    console.log('Rendering connections:', edges);
    return edges.map((edge, index) => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);

      if (!sourceNode || !targetNode) {
        console.log('Missing node for edge:', edge);
        return null;
      }

      const startX = sourceNode.position.x + 140; // Center of node
      const startY = sourceNode.position.y + 120; // Bottom of node
      const endX = targetNode.position.x + 140; // Center of node
      const endY = targetNode.position.y; // Top of node

      console.log('Drawing connection:', { startX, startY, endX, endY });

      return (
        <g key={`edge-${index}`}>
          <path
            d={`M ${startX} ${startY} C ${startX} ${startY + 50} ${endX} ${endY - 50} ${endX} ${endY}`}
            stroke="#3b82f6"
            strokeWidth="3"
            fill="none"
            markerEnd="url(#arrowhead)"
            className="pointer-events-none"
          />
          {/* Connection label if exists */}
          {edge.label && (
            <text
              x={(startX + endX) / 2}
              y={(startY + endY) / 2}
              textAnchor="middle"
              className="fill-gray-600 text-xs font-medium"
              style={{ pointerEvents: 'none' }}
            >
              {edge.label}
            </text>
          )}
        </g>
      );
    });
  };

  return (
    <div className={`relative w-full h-full bg-white overflow-hidden ${dragState.isDragging ? 'select-none' : ''}`}>
      {/* Debug indicator */}
      {dragState.isDragging && (
        <div className="absolute top-4 left-4 z-50 bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium">
          Dragging {dragState.dragType}: {dragState.draggedNode?.id || 'canvas'}
        </div>
      )}
      {/* Canvas */}
      <div
        ref={canvasRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
        onMouseDown={handleCanvasMouseDown}
        onWheel={handleWheel}
        style={{
          backgroundImage: `radial-gradient(circle, #e2e8f0 1px, transparent 1px)`,
          backgroundSize: `${20 * canvasTransform.scale}px ${20 * canvasTransform.scale}px`,
          backgroundPosition: `${canvasTransform.x}px ${canvasTransform.y}px`,
        }}
      >
        {/* SVG for connections */}
        <svg
          ref={svgRef}
          className="absolute inset-0 pointer-events-none w-full h-full"
          style={{
            transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`,
            transformOrigin: '0 0',
            zIndex: 1,
          }}
        >
          <defs>
            <marker
              id="arrowhead"
              markerWidth="12"
              markerHeight="8"
              refX="11"
              refY="4"
              orient="auto"
              markerUnits="strokeWidth"
            >
              <polygon
                points="0 0, 12 4, 0 8"
                fill="#3b82f6"
              />
            </marker>
          </defs>
          {renderConnections()}
          {/* Temporary connection line */}
          {tempConnection && (
            <line
              x1={tempConnection.start.x / canvasTransform.scale - canvasTransform.x / canvasTransform.scale}
              y1={tempConnection.start.y / canvasTransform.scale - canvasTransform.y / canvasTransform.scale}
              x2={tempConnection.end.x / canvasTransform.scale - canvasTransform.x / canvasTransform.scale}
              y2={tempConnection.end.y / canvasTransform.scale - canvasTransform.y / canvasTransform.scale}
              stroke="#3b82f6"
              strokeWidth="3"
              strokeDasharray="8,4"
              opacity="0.7"
            />
          )}
        </svg>

        {/* Nodes */}
        <div
          style={{
            transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`,
            transformOrigin: '0 0',
            position: 'relative',
            zIndex: 2,
          }}
        >
          {nodes.map((node) => {
            const NodeComponent = nodeTypes[node.type];
            if (!NodeComponent) return null;

            // Use drag preview position if this node is being dragged
            const position = dragPreview && dragPreview.nodeId === node.id
              ? dragPreview.position
              : node.position;

            const isDragging = dragPreview && dragPreview.nodeId === node.id;

            return (
              <div
                key={node.id}
                className={`absolute cursor-move transition-shadow ${
                  isDragging ? 'shadow-2xl' : ''
                }`}
                style={{
                  left: position.x,
                  top: position.y,
                  transform: 'translate(0, 0)',
                  zIndex: isDragging ? 1000 : 1,
                  opacity: isDragging ? 0.9 : 1,
                }}
                draggable={false}
                onMouseDown={(e) => handleNodeMouseDown(e, node)}
                onClick={(e) => {
                  // Only trigger click if it wasn't a drag operation (check time difference)
                  const timeDiff = Date.now() - dragState.startTime;
                  console.log('Node click - timeDiff:', timeDiff, 'isDragging:', dragState.isDragging, 'startTime:', dragState.startTime);
                  if (!dragState.isDragging && timeDiff < 500) {
                    e.preventDefault();
                    e.stopPropagation();
                    onNodeClick(node);
                  }
                }}
                onDragStart={(e) => e.preventDefault()}
              >
                <NodeComponent
                  data={node.data}
                  selected={selectedNode?.id === node.id}
                />
                {/* Connection handles */}
                <div
                  className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-crosshair hover:bg-blue-600 transition-colors"
                  style={{ top: -8, left: '50%', transform: 'translateX(-50%)', zIndex: 10 }}
                  title="Input connection"
                />
                <div
                  className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-crosshair hover:bg-blue-600 transition-colors"
                  style={{ bottom: -8, left: '50%', transform: 'translateX(-50%)', zIndex: 10 }}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                    handleConnectionStart(e, node.id);
                  }}
                  title="Output connection - drag to connect"
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Controls */}
      <div className="absolute bottom-4 right-4 flex flex-col gap-2">
        <button
          className="w-10 h-10 bg-white border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
          onClick={() => setCanvasTransform(prev => ({ ...prev, scale: Math.min(3, prev.scale * 1.2) }))}
        >
          +
        </button>
        <button
          className="w-10 h-10 bg-white border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
          onClick={() => setCanvasTransform(prev => ({ ...prev, scale: Math.max(0.1, prev.scale * 0.8) }))}
        >
          −
        </button>
        <button
          className="w-10 h-10 bg-white border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
          onClick={() => setCanvasTransform({ x: 0, y: 0, scale: 1 })}
        >
          ⌂
        </button>
      </div>
    </div>
  );
};

export default VapiCanvas;
